<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.system.mapper.ExportTaskMapper">
    
    <resultMap type="ExportTask" id="ExportTaskResult">
        <result property="taskId"    column="task_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="taskType"    column="task_type"    />
        <result property="status"    column="status"    />
        <result property="progress"    column="progress"    />
        <result property="message"    column="message"    />
        <result property="filePath"    column="file_path"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileSize"    column="file_size"    />
        <result property="startTime"    column="start_time"    />
        <result property="finishTime"    column="finish_time"    />
        <result property="expireTime"    column="expire_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectExportTaskVo">
        select task_id, user_id, user_name, task_type, status, progress, message, file_path, file_name, file_size, start_time, finish_time, expire_time, create_time, update_time from export_task
    </sql>

    <select id="selectExportTaskList" parameterType="ExportTask" resultMap="ExportTaskResult">
        <include refid="selectExportTaskVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="taskType != null  and taskType != ''"> and task_type = #{taskType}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectExportTaskByTaskId" parameterType="String" resultMap="ExportTaskResult">
        <include refid="selectExportTaskVo"/>
        where task_id = #{taskId}
    </select>

    <select id="selectExpiredTasks" resultMap="ExportTaskResult">
        <include refid="selectExportTaskVo"/>
        where expire_time &lt; now()
    </select>
        
    <insert id="insertExportTask" parameterType="ExportTask">
        insert into export_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="taskType != null">task_type,</if>
            <if test="status != null">status,</if>
            <if test="progress != null">progress,</if>
            <if test="message != null">message,</if>
            <if test="filePath != null">file_path,</if>
            <if test="fileName != null">file_name,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="startTime != null">start_time,</if>
            <if test="finishTime != null">finish_time,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="taskType != null">#{taskType},</if>
            <if test="status != null">#{status},</if>
            <if test="progress != null">#{progress},</if>
            <if test="message != null">#{message},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="finishTime != null">#{finishTime},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateExportTask" parameterType="ExportTask">
        update export_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="taskType != null">task_type = #{taskType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="progress != null">progress = #{progress},</if>
            <if test="message != null">message = #{message},</if>
            <if test="filePath != null">file_path = #{filePath},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="finishTime != null">finish_time = #{finishTime},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where task_id = #{taskId}
    </update>

    <delete id="deleteExportTaskByTaskId" parameterType="String">
        delete from export_task where task_id = #{taskId}
    </delete>

    <delete id="deleteExportTaskByTaskIds" parameterType="String">
        delete from export_task where task_id in 
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>
</mapper>
