// 在原有的ProjectInfoController中添加以下方法

import com.ruoyi.project.system.service.IExportTaskService;
import com.ruoyi.project.system.domain.ExportTask;
import org.springframework.scheduling.annotation.Async;
import java.util.concurrent.CompletableFuture;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;

@Autowired
private IExportTaskService exportTaskService;

/**
 * 异步导出全部作品信息
 *
 * @return 任务ID
 */
@PostMapping("/exportAllAsync")
public AjaxResult exportAllAsync() {
    LoginUser loginUser = SecurityUtils.getLoginUser();
    Long userId = loginUser.getUserId();
    String userName = loginUser.getUsername();

    // 检查频率限制
    if (!exportTaskService.canCreateExportTask(userId)) {
        return AjaxResult.error("导出操作过于频繁，请5分钟后再试");
    }

    // 创建异步导出任务
    String taskId = exportTaskService.createAsyncExportTask(userId, userName, "PROJECT_EXPORT_ALL");

    // 异步执行导出
    executeAsyncExport(taskId);

    Map<String, Object> result = new HashMap<>();
    result.put("taskId", taskId);
    result.put("message", "导出任务已启动");

    return AjaxResult.success(result);
}

/**
 * 查询导出任务状态
 *
 * @param taskId 任务ID
 * @return 任务状态
 */
@GetMapping("/exportStatus/{taskId}")
public AjaxResult getExportStatus(@PathVariable String taskId) {
    ExportTask task = exportTaskService.selectExportTaskByTaskId(taskId);

    if (task == null) {
        return AjaxResult.error("任务不存在");
    }

    // 检查任务是否属于当前用户
    LoginUser loginUser = SecurityUtils.getLoginUser();
    if (!task.getUserId().equals(loginUser.getUserId())) {
        return AjaxResult.error("无权访问此任务");
    }

    Map<String, Object> result = new HashMap<>();
    result.put("taskId", task.getTaskId());
    result.put("status", task.getStatus());
    result.put("progress", task.getProgress());
    result.put("message", task.getMessage());
    result.put("startTime", task.getStartTime());
    result.put("finishTime", task.getFinishTime());

    return AjaxResult.success(result);
}

/**
 * 下载导出文件
 *
 * @param taskId 任务ID
 * @return 文件下载
 */
@GetMapping("/downloadExport/{taskId}")
public ResponseEntity<ByteArrayResource> downloadExportFile(@PathVariable String taskId) {
    ExportTask task = exportTaskService.selectExportTaskByTaskId(taskId);

    if (task == null) {
        return ResponseEntity.notFound().build();
    }

    // 检查任务是否属于当前用户
    LoginUser loginUser = SecurityUtils.getLoginUser();
    if (!task.getUserId().equals(loginUser.getUserId())) {
        return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
    }

    // 检查任务是否完成
    if (!"completed".equals(task.getStatus())) {
        return ResponseEntity.badRequest().build();
    }

    // 检查文件是否存在
    File file = new File(task.getFilePath());
    if (!file.exists()) {
        return ResponseEntity.notFound().build();
    }

    try {
        byte[] bytes = Files.readAllBytes(Paths.get(task.getFilePath()));

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        "attachment; filename=\"" + URLEncoder.encode(task.getFileName(), StandardCharsets.UTF_8.toString()) + "\"")
                .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                .contentLength(bytes.length)
                .body(new ByteArrayResource(bytes));
    } catch (Exception e) {
        log.error("下载导出文件失败", e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }
}

/**
 * 获取用户导出历史
 *
 * @return 导出历史列表
 */
@GetMapping("/exportHistory")
public AjaxResult getExportHistory() {
    LoginUser loginUser = SecurityUtils.getLoginUser();

    ExportTask queryTask = new ExportTask();
    queryTask.setUserId(loginUser.getUserId());

    List<ExportTask> history = exportTaskService.selectExportTaskList(queryTask);

    return AjaxResult.success(history);
}

/**
 * 异步执行导出任务
 *
 * @param taskId 任务ID
 */
@Async
public CompletableFuture<Void> executeAsyncExport(String taskId) {
    try {
        // 更新任务状态为处理中
        exportTaskService.updateTaskStatus(taskId, "processing", 10, "开始查询作品数据...");

        // 查询所有作品信息
        List<ProjectInfo> projectInfoList = projectInfoService.queryAll();

        if (CollectionUtils.isEmpty(projectInfoList)) {
            exportTaskService.failTask(taskId, "没有找到作品数据");
            return CompletableFuture.completedFuture(null);
        }

        exportTaskService.updateTaskStatus(taskId, "processing", 30, "正在处理作品数据...");

        // 准备导出数据（这里复用原有的导出逻辑）
        List<ProjectExportVo> exportList = new ArrayList<>();
        int totalCount = projectInfoList.size();
        int processedCount = 0;

        for (ProjectInfo info : projectInfoList) {
            ProjectExportVo vo = new ProjectExportVo();
            vo.setName(info.getName());
            vo.setCategory(getCategoryName(info.getCategory()));
            vo.setAddressContent(info.getAddressContent());
            vo.setTutorName(info.getTutorName());
            vo.setTutorContact(info.getTutorContact());
            vo.setDescriptionText(info.getDescriptionText());
            vo.setParticipantType(getParticipantTypeName(info.getParticipantType()));
            vo.setParticipantCount(info.getParticipantCount());
            vo.setArticleStatus(getArticleStatusName(info.getArticleStatus()));
            vo.setDistrictAuditor(info.getDistrictAuditor());
            vo.setDistrictAuditStatus(getDistrictAuditStatusName(info.getDistrictAuditStatus()));
            vo.setDistrictAuditRemark(info.getDistrictAuditRemark());
            vo.setAuditor(info.getAuditor());
            vo.setAuditStatus(getAuditStatusName(info.getAuditStatus()));
            vo.setAuditRemark(info.getAuditRemark());

            // 获取作者姓名和团队成员姓名
            SysUser user = userService.selectUserByUserName(info.getUserName());
            String nickName = user.getNickName();
            vo.setAuthor(nickName);

            Long userId = user.getUserId();
            List<SysTeamMember> teamMembers = teamMemberService.selectTeamMembersByUserId(userId);
            if (CollectionUtils.isNotEmpty(teamMembers)) {
                for (int i = 1; i < teamMembers.size() + 1; i++) {
                    SysTeamMember teamMember = teamMembers.get(i - 1);
                    if (i == 1) {
                        vo.setOneTeamName(teamMember.getName());
                    } else if (i == 2) {
                        vo.setTwoTeamName(teamMember.getName());
                    }
                }
            }

            exportList.add(vo);

            // 更新进度
            processedCount++;
            int progress = 30 + (int) ((processedCount * 50.0) / totalCount);
            exportTaskService.updateTaskStatus(taskId, "processing", progress,
                String.format("正在处理作品数据 %d/%d", processedCount, totalCount));
        }

        exportTaskService.updateTaskStatus(taskId, "processing", 80, "正在生成Excel文件...");

        // 生成Excel文件
        String fileName = "作品信息表_" + System.currentTimeMillis() + ".xlsx";
        String uploadPath = RuoYiConfig.getUploadPath();
        String exportDir = uploadPath + "/export/";

        // 确保导出目录存在
        File dir = new File(exportDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        String filePath = exportDir + fileName;

        // 使用EasyExcel生成文件（复用原有逻辑）
        generateExcelFile(exportList, filePath);

        // 获取文件大小
        File file = new File(filePath);
        long fileSize = file.length();

        // 完成任务
        exportTaskService.completeTask(taskId, filePath, fileName, fileSize);

    } catch (Exception e) {
        log.error("异步导出任务执行失败，taskId: " + taskId, e);
        exportTaskService.failTask(taskId, "导出失败: " + e.getMessage());
    }

    return CompletableFuture.completedFuture(null);
}

/**
 * 生成Excel文件
 *
 * @param exportList 导出数据
 * @param filePath 文件路径
 */
private void generateExcelFile(List<ProjectExportVo> exportList, String filePath) throws Exception {
    // 设置表头样式
    WriteCellStyle headWriteCellStyle = new WriteCellStyle();
    WriteFont headWriteFont = new WriteFont();
    headWriteFont.setFontHeightInPoints((short)14);
    headWriteFont.setBold(true);
    headWriteCellStyle.setWriteFont(headWriteFont);

    // 设置内容样式
    WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
    WriteFont contentWriteFont = new WriteFont();
    contentWriteFont.setFontHeightInPoints((short)12);
    contentWriteCellStyle.setWriteFont(contentWriteFont);

    HorizontalCellStyleStrategy styleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);

    EasyExcel.write(filePath, ProjectExportVo.class)
            .registerWriteHandler(styleStrategy)
            .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
            .registerWriteHandler(new SheetWriteHandler() {
                @Override
                public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
                    for (int i = 0; i < 15; i++) {
                        if (i == 5) {
                            writeSheetHolder.getSheet().setColumnWidth(i, 50 * 256);
                        } else {
                            writeSheetHolder.getSheet().setColumnWidth(i, 20 * 256);
                        }
                    }
                }

                @Override
                public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
                }
            })
            .sheet("作品信息")
            .doWrite(exportList);
}
