package com.ruoyi.project.system.service;

import com.ruoyi.project.system.domain.ExportTask;
import java.util.List;

/**
 * 导出任务Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IExportTaskService 
{
    /**
     * 查询导出任务
     * 
     * @param taskId 导出任务主键
     * @return 导出任务
     */
    public ExportTask selectExportTaskByTaskId(String taskId);

    /**
     * 查询导出任务列表
     * 
     * @param exportTask 导出任务
     * @return 导出任务集合
     */
    public List<ExportTask> selectExportTaskList(ExportTask exportTask);

    /**
     * 新增导出任务
     * 
     * @param exportTask 导出任务
     * @return 结果
     */
    public int insertExportTask(ExportTask exportTask);

    /**
     * 修改导出任务
     * 
     * @param exportTask 导出任务
     * @return 结果
     */
    public int updateExportTask(ExportTask exportTask);

    /**
     * 批量删除导出任务
     * 
     * @param taskIds 需要删除的导出任务主键集合
     * @return 结果
     */
    public int deleteExportTaskByTaskIds(String[] taskIds);

    /**
     * 删除导出任务信息
     * 
     * @param taskId 导出任务主键
     * @return 结果
     */
    public int deleteExportTaskByTaskId(String taskId);

    /**
     * 创建异步导出任务
     * 
     * @param userId 用户ID
     * @param userName 用户名
     * @param taskType 任务类型
     * @return 任务ID
     */
    public String createAsyncExportTask(Long userId, String userName, String taskType);

    /**
     * 更新任务状态
     * 
     * @param taskId 任务ID
     * @param status 状态
     * @param progress 进度
     * @param message 消息
     */
    public void updateTaskStatus(String taskId, String status, Integer progress, String message);

    /**
     * 完成任务
     * 
     * @param taskId 任务ID
     * @param filePath 文件路径
     * @param fileName 文件名
     * @param fileSize 文件大小
     */
    public void completeTask(String taskId, String filePath, String fileName, Long fileSize);

    /**
     * 任务失败
     * 
     * @param taskId 任务ID
     * @param message 失败消息
     */
    public void failTask(String taskId, String message);

    /**
     * 检查用户是否可以创建新的导出任务（频率限制）
     * 
     * @param userId 用户ID
     * @return 是否可以创建
     */
    public boolean canCreateExportTask(Long userId);

    /**
     * 清理过期的导出任务和文件
     */
    public void cleanExpiredTasks();
}
