<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
  <link rel="icon" href="<%= BASE_URL %>favicon.ico">
  <title><%= webpackConfig.name %></title>
  <!--[if lt IE 11]><script>window.location.href='/html/ie.html';</script><![endif]-->
  <!-- <link rel="stylesheet" href="./index.css"> -->
  <style>
    html,
    body,
    #app {
      height: 100%;
      margin: 0px;
      padding: 0px;
    }
    .chromeframe {
      margin: 0.2em 0;
      background: #ccc;
      color: #000;
      padding: 0.2em 0;
    }

    #loader-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999999;
      display:flex;
      justify-content: center ; align-items:center;
      background-image:linear-gradient(to right top,#051937,#004d7a,#008793,#00bf72,#a8eb12);
      font-family:'Noto Sans',sans-serif;
      color:white;
      text-align:center;
      letter-spacing:.3px
    }
    .loader{
      --color:white;
      --size-mid:6vmin;
      --size-dot:1.5vmin;
      --size-bar:.4vmin;
      --size-square:3vmin;
      display:block;
      position:relative;
      width:50%;
      display:grid;
      place-items:center
    }
    .loader::before,.loader::after{
      content:'';
      box-sizing:border-box;
      position:absolute
    }
    .loader.--1::before{
      width:var(--size-mid);
      height:var(--size-mid);
      border:4px solid var(--color);
      border-top-color:transparent;
      border-radius:50%;
      -webkit-animation:loader-1 1s linear infinite;
      animation:loader-1 1s linear infinite
    }
    .loader.--1::after{
      width:calc(var(--size-mid) - 2px);
      height:calc(var(--size-mid) - 2px);
      border:2px solid transparent;
      border-top-color:var(--color);
      border-radius:50%;
      animation:loader-1 .6s linear reverse infinite
    }
    @-webkit-keyframes loader-1{
      100%{transform:rotate(1turn)}
    }
    @keyframes loader-1{
      100%{transform:rotate(1turn)}
    }
    .loader.--2::before,.loader.--2::after{
      width:var(--size-dot);
      height:var(--size-dot);
      background-color:var(--color);
      border-radius:50%;
      opacity:0;
      -webkit-animation:loader-2 .8s cubic-bezier(0.2,0.32,0,0.87) infinite;
      animation:loader-2 .8s cubic-bezier(0.2,0.32,0,0.87) infinite
    }
    .loader.--2::after{
      -webkit-animation-delay:.3s;
      animation-delay:.3s
    }
    @-webkit-keyframes loader-2{
      0%,80%,100%{opacity:0}
      33%{opacity:1}
      0%,100%{transform:translateX(-4vmin)}
      90%{transform:translateX(4vmin)}
    }
    @keyframes loader-2{
      0%,80%,100%{opacity:0}
      33%{opacity:1}
      0%,100%{transform:translateX(-4vmin)}
      90%{transform:translateX(4vmin)}
    }
    .loader.--3::before,.loader.--3::after{
      width:var(--size-dot);
      height:var(--size-dot);
      background-color:var(--color);
      border-radius:50%;
      -webkit-animation:loader-3 1.2s ease-in-out infinite;
      animation:loader-3 1.2s ease-in-out infinite
    }
    .loader.--3::before{
      left:calc(50% - 1.6vmin - var(--size-dot))
    }
    .loader.--3::after{
      left:calc(50%+1.6vmin);
      -webkit-animation-delay:-0.4s;
      animation-delay:-0.4s
    }
    @-webkit-keyframes loader-3{
      0%,100%{transform:translateY(-2.6vmin)}
      44%{transform:translateY(2.6vmin)}
    }
    @keyframes loader-3{
      0%,100%{transform:translateY(-2.6vmin)}
      44%{transform:translateY(2.6vmin)}
    }
    .loader.--4::before{
      height:var(--size-bar);
      width:6vmin;
      background-color:var(--color);
      -webkit-animation:loader-4 .8s cubic-bezier(0,0,0.03,0.9) infinite;
      animation:loader-4 .8s cubic-bezier(0,0,0.03,0.9) infinite
    }
    @-webkit-keyframes loader-4{
      0%,44%,88.1%,100%{transform-origin:left}
      0%,100%,88%{transform:scaleX(0)}
      44.1%,88%{transform-origin:right}
      33%,44%{transform:scaleX(1)}
    }
    @keyframes loader-4{
      0%,44%,88.1%,100%{transform-origin:left}
      0%,100%,88%{transform:scaleX(0)}
      44.1%,88%{transform-origin:right}
      33%,44%{transform:scaleX(1)}
    }
    .loader.--5::before,.loader.--5::after{
      height:3vmin;
      width:var(--size-bar);
      background-color:var(--color);
      -webkit-animation:loader-5 .6s cubic-bezier(0,0,0.03,0.9) infinite;
      animation:loader-5 .6s cubic-bezier(0,0,0.03,0.9) infinite
    }
    .loader.--5::before{
      left:calc(50% - 1vmin);
      top:calc(50% - 3vmin)
    }
    .loader.--5::after{
      left:calc(50%+1vmin);
      top:calc(50% - 1vmin);
      -webkit-animation-delay:.2s;animation-delay:.2s
    }
    @-webkit-keyframes loader-5{
      0%,88%,100%{opacity:0}
      0%{transform:translateY(-6vmin)}
      33%{opacity:1}
      33%,88%{transform:translateY(3vmin)}
    }
    @keyframes loader-5{
      0%,88%,100%{opacity:0}
      0%{transform:translateY(-6vmin)}
      33%{opacity:1}
      33%,88%{transform:translateY(3vmin)}
    }
    .loader.--6::before{
      width:var(--size-square);
      height:var(--size-square);
      background-color:var(--color);
      top:calc(50% - var(--size-square));
      left:calc(50% - var(--size-square));
      -webkit-animation:loader-6 2.4s cubic-bezier(0,0,0.24,1.21) infinite;
      animation:loader-6 2.4s cubic-bezier(0,0,0.24,1.21) infinite
    }
    @-webkit-keyframes loader-6{
      0%,100%{transform:none}
      25%{transform:translateX(100%)}
      50%{transform:translateX(100%) translateY(100%)}
      75%{transform:translateY(100%)}
    }
    @keyframes loader-6{
      0%,100%{transform:none}
      25%{transform:translateX(100%)}
      50%{transform:translateX(100%) translateY(100%)}
      75%{transform:translateY(100%)}
    }
    .loader.--7::before,.loader.--7::after{
      width:var(--size-square);
      height:var(--size-square);
      background-color:var(--color)
    }
    .loader.--7::before{
      top:calc(50% - var(--size-square));
      left:calc(50% - var(--size-square));
      -webkit-animation:loader-6 2.4s cubic-bezier(0,0,0.24,1.21) infinite;
      animation:loader-6 2.4s cubic-bezier(0,0,0.24,1.21) infinite
    }
    .loader.--7::after{
      top:50%;
      left:50%;
      -webkit-animation:loader-7 2.4s cubic-bezier(0,0,0.24,1.21) infinite;
      animation:loader-7 2.4s cubic-bezier(0,0,0.24,1.21) infinite
    }
    @-webkit-keyframes loader-7{
      0%,100%{transform:none}
      25%{transform:translateX(-100%)}
      50%{transform:translateX(-100%) translateY(-100%)}
      75%{transform:translateY(-100%)}
    }
    @keyframes loader-7{
      0%,100%{transform:none}
      25%{transform:translateX(-100%)}
      50%{transform:translateX(-100%) translateY(-100%)}
      75%{transform:translateY(-100%)}
    }
    .loader.--8::before,.loader.--8::after{
      width:var(--size-dot);
      height:var(--size-dot);
      border-radius:50%;
      background-color:var(--color)
    }
    .loader.--8::before{
      top:calc(50%+4vmin);
      -webkit-animation:loader-8-1 .8s cubic-bezier(0.06,0.01,0.49,1.18) infinite;
      animation:loader-8-1 .8s cubic-bezier(0.06,0.01,0.49,1.18) infinite
    }
    .loader.--8::after{
      opacity:0;
      top:calc(50% - 2vmin);
      -webkit-animation:loader-8-2 .8s cubic-bezier(0.46,-0.1,0.27,1.07) .2s infinite;
      animation:loader-8-2 .8s cubic-bezier(0.46,-0.1,0.27,1.07) .2s infinite
    }
    @-webkit-keyframes loader-8-1{
      0%,55%,100%{opacity:0}
      0%{transform:scale(0.2)}
      22%{opacity:1}
      33%,55%{transform:scale(1) translateY(-6vmin)}
    }
    @keyframes loader-8-1{
      0%,55%,100%{opacity:0}
      0%{transform:scale(0.2)}
      22%{opacity:1}
      33%,55%{transform:scale(1) translateY(-6vmin)}
    }
    @-webkit-keyframes loader-8-2{
      0%,100%{opacity:0}
      33%{opacity:.3}
      0%{transform:scale(0)}
      100%{transform:scale(4)}
    }
    @keyframes loader-8-2{
      0%,100%{opacity:0}
      33%{opacity:.3}
      0%{transform:scale(0)}
      100%{transform:scale(4)}
    }
    .loader.--9::before,.loader.--9::after{
      width:var(--size-dot);
      height:var(--size-dot);
      border-radius:50%;
      background-color:var(--color);
      -webkit-animation:loader-9 .42s cubic-bezier(0.39,0.31,0,1.11) infinite;
      animation:loader-9 .42s cubic-bezier(0.39,0.31,0,1.11) infinite
    }
    .loader.--9::before{
      left:calc(50% - var(--size-dot) - 1.6vmin)
    }
    .loader.--9::after{
      left:calc(50%+1.6vmin);
      -webkit-animation-delay:.12s;animation-delay:.12s
    }
    @-webkit-keyframes loader-9{
      0%,100%{opacity:0}
      0%{transform:translate(-4vmin,-4vmin)}
      66%{opacity:1}
      66%,100%{transform:none}
    }
    @keyframes loader-9{
      0%,100%{opacity:0}
      0%{transform:translate(-4vmin,-4vmin)}
      66%{opacity:1}
      66%,100%{transform:none}
    }
    .container{
      display:grid;
      grid-template-columns:repeat(3,18vmin);
      grid-template-rows:repeat(3,18vmin);
      grid-gap:1vmin
    }
    .item{
      background:rgba(255,255,255,0.1);
      display:grid;
      place-items:center;
      border-radius:4px
    }
    .page{
      margin:auto
    }
    .header{
      margin-bottom:4vmin
    }
    .header-title{
      font-size:3.75vmin
    }
    .header-subtitle{
      font-size:2vmin
    ;text-transform:uppercase;
      opacity:.6
    }
  </style>
</head>
<body>
<div id="app">
  <div id="loader-wrapper">
    <div class="page">
      <h2 class="header">系统资源加载中...再等等哦!</h2>
      <main class="container">
        <div class="item">
          <i class="loader --2"></i>
        </div>
        <div class="item">
          <i class="loader --9"></i>
        </div>
        <div class="item">
          <i class="loader --3"></i>
        </div>
        <div class="item">
          <i class="loader --4"></i>
        </div>
        <div class="item">
          <i class="loader --1"></i>
        </div>
        <div class="item">
          <i class="loader --5"></i>
        </div>
        <div class="item">
          <i class="loader --6"></i>
        </div>
        <div class="item">
          <i class="loader --8"></i>
        </div>
        <div class="item">
          <i class="loader --7"></i>
        </div>
      </main>
    </div>
  </div>

</div>
</body>
</html>
