-- 导出任务表
CREATE TABLE `export_task` (
  `task_id` varchar(32) NOT NULL COMMENT '任务ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `user_name` varchar(30) NOT NULL COMMENT '用户名',
  `task_type` varchar(50) NOT NULL COMMENT '任务类型',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '任务状态：pending-等待中，processing-处理中，completed-已完成，failed-失败',
  `progress` int(3) DEFAULT '0' COMMENT '进度百分比',
  `message` varchar(500) DEFAULT NULL COMMENT '状态消息',
  `file_path` varchar(500) DEFAULT NULL COMMENT '文件路径',
  `file_name` varchar(200) DEFAULT NULL COMMENT '文件名',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `finish_time` datetime DEFAULT NULL COMMENT '完成时间',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`task_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='导出任务表';

-- 插入菜单权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('异步导出全部', (SELECT menu_id FROM sys_menu WHERE menu_name = '作品管理' LIMIT 1), 6, '', '', 1, 0, 'F', '0', '0', 'system:project:exportAll', '#', 'admin', sysdate(), '', null, '');

-- 定时清理过期任务的存储过程（可选）
DELIMITER $$
CREATE PROCEDURE CleanExpiredExportTasks()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE task_file_path VARCHAR(500);
    DECLARE cur CURSOR FOR 
        SELECT file_path FROM export_task 
        WHERE expire_time < NOW() AND file_path IS NOT NULL;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- 删除过期文件
    OPEN cur;
    read_loop: LOOP
        FETCH cur INTO task_file_path;
        IF done THEN
            LEAVE read_loop;
        END IF;
        -- 这里可以添加删除文件的逻辑，但MySQL无法直接删除文件
        -- 建议通过应用程序定时任务来处理
    END LOOP;
    CLOSE cur;
    
    -- 删除过期任务记录
    DELETE FROM export_task WHERE expire_time < NOW();
END$$
DELIMITER ;
