package com.ruoyi.project.system.service.impl;

import java.io.File;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import com.ruoyi.project.system.mapper.ExportTaskMapper;
import com.ruoyi.project.system.domain.ExportTask;
import com.ruoyi.project.system.service.IExportTaskService;

/**
 * 导出任务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class ExportTaskServiceImpl implements IExportTaskService 
{
    @Autowired
    private ExportTaskMapper exportTaskMapper;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String EXPORT_LIMIT_KEY = "export_limit:";
    private static final int EXPORT_LIMIT_MINUTES = 5; // 5分钟限制

    /**
     * 查询导出任务
     * 
     * @param taskId 导出任务主键
     * @return 导出任务
     */
    @Override
    public ExportTask selectExportTaskByTaskId(String taskId)
    {
        return exportTaskMapper.selectExportTaskByTaskId(taskId);
    }

    /**
     * 查询导出任务列表
     * 
     * @param exportTask 导出任务
     * @return 导出任务
     */
    @Override
    public List<ExportTask> selectExportTaskList(ExportTask exportTask)
    {
        return exportTaskMapper.selectExportTaskList(exportTask);
    }

    /**
     * 新增导出任务
     * 
     * @param exportTask 导出任务
     * @return 结果
     */
    @Override
    public int insertExportTask(ExportTask exportTask)
    {
        exportTask.setCreateTime(DateUtils.getNowDate());
        return exportTaskMapper.insertExportTask(exportTask);
    }

    /**
     * 修改导出任务
     * 
     * @param exportTask 导出任务
     * @return 结果
     */
    @Override
    public int updateExportTask(ExportTask exportTask)
    {
        exportTask.setUpdateTime(DateUtils.getNowDate());
        return exportTaskMapper.updateExportTask(exportTask);
    }

    /**
     * 批量删除导出任务
     * 
     * @param taskIds 需要删除的导出任务主键
     * @return 结果
     */
    @Override
    public int deleteExportTaskByTaskIds(String[] taskIds)
    {
        return exportTaskMapper.deleteExportTaskByTaskIds(taskIds);
    }

    /**
     * 删除导出任务信息
     * 
     * @param taskId 导出任务主键
     * @return 结果
     */
    @Override
    public int deleteExportTaskByTaskId(String taskId)
    {
        return exportTaskMapper.deleteExportTaskByTaskId(taskId);
    }

    /**
     * 创建异步导出任务
     */
    @Override
    public String createAsyncExportTask(Long userId, String userName, String taskType) {
        String taskId = UUID.randomUUID().toString().replace("-", "");
        
        ExportTask task = new ExportTask();
        task.setTaskId(taskId);
        task.setUserId(userId);
        task.setUserName(userName);
        task.setTaskType(taskType);
        task.setStatus("pending");
        task.setProgress(0);
        task.setMessage("任务已创建，等待处理");
        task.setStartTime(new Date());
        
        // 设置过期时间（24小时后）
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR_OF_DAY, 24);
        task.setExpireTime(calendar.getTime());
        
        insertExportTask(task);
        
        // 设置Redis限制
        String limitKey = EXPORT_LIMIT_KEY + userId;
        redisTemplate.opsForValue().set(limitKey, taskId, EXPORT_LIMIT_MINUTES, TimeUnit.MINUTES);
        
        return taskId;
    }

    /**
     * 更新任务状态
     */
    @Override
    public void updateTaskStatus(String taskId, String status, Integer progress, String message) {
        ExportTask task = new ExportTask();
        task.setTaskId(taskId);
        task.setStatus(status);
        task.setProgress(progress);
        task.setMessage(message);
        updateExportTask(task);
    }

    /**
     * 完成任务
     */
    @Override
    public void completeTask(String taskId, String filePath, String fileName, Long fileSize) {
        ExportTask task = new ExportTask();
        task.setTaskId(taskId);
        task.setStatus("completed");
        task.setProgress(100);
        task.setMessage("导出完成");
        task.setFilePath(filePath);
        task.setFileName(fileName);
        task.setFileSize(fileSize);
        task.setFinishTime(new Date());
        updateExportTask(task);
    }

    /**
     * 任务失败
     */
    @Override
    public void failTask(String taskId, String message) {
        ExportTask task = new ExportTask();
        task.setTaskId(taskId);
        task.setStatus("failed");
        task.setMessage(message);
        task.setFinishTime(new Date());
        updateExportTask(task);
    }

    /**
     * 检查用户是否可以创建新的导出任务
     */
    @Override
    public boolean canCreateExportTask(Long userId) {
        String limitKey = EXPORT_LIMIT_KEY + userId;
        return !redisTemplate.hasKey(limitKey);
    }

    /**
     * 清理过期的导出任务和文件
     */
    @Override
    public void cleanExpiredTasks() {
        ExportTask queryTask = new ExportTask();
        List<ExportTask> expiredTasks = exportTaskMapper.selectExpiredTasks();
        
        for (ExportTask task : expiredTasks) {
            // 删除文件
            if (StringUtils.isNotEmpty(task.getFilePath())) {
                File file = new File(task.getFilePath());
                if (file.exists()) {
                    file.delete();
                }
            }
            
            // 删除任务记录
            deleteExportTaskByTaskId(task.getTaskId());
        }
    }
}
