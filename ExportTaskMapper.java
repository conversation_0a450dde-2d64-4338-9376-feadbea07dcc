package com.ruoyi.project.system.mapper;

import java.util.List;
import com.ruoyi.project.system.domain.ExportTask;

/**
 * 导出任务Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ExportTaskMapper 
{
    /**
     * 查询导出任务
     * 
     * @param taskId 导出任务主键
     * @return 导出任务
     */
    public ExportTask selectExportTaskByTaskId(String taskId);

    /**
     * 查询导出任务列表
     * 
     * @param exportTask 导出任务
     * @return 导出任务集合
     */
    public List<ExportTask> selectExportTaskList(ExportTask exportTask);

    /**
     * 新增导出任务
     * 
     * @param exportTask 导出任务
     * @return 结果
     */
    public int insertExportTask(ExportTask exportTask);

    /**
     * 修改导出任务
     * 
     * @param exportTask 导出任务
     * @return 结果
     */
    public int updateExportTask(ExportTask exportTask);

    /**
     * 删除导出任务
     * 
     * @param taskId 导出任务主键
     * @return 结果
     */
    public int deleteExportTaskByTaskId(String taskId);

    /**
     * 批量删除导出任务
     * 
     * @param taskIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExportTaskByTaskIds(String[] taskIds);

    /**
     * 查询过期的导出任务
     * 
     * @return 过期任务列表
     */
    public List<ExportTask> selectExpiredTasks();
}
