<template>
  <div class="login">
    <el-form
      v-if="!showForgotPassword"
      ref="loginForm"
      :model="loginForm"
      :rules="loginRules"
      class="login-form"
    >
      <h3 class="title">{{ title }}</h3>
      <el-form-item prop="username">
        <el-input
          v-model="loginForm.username"
          type="text"
          auto-complete="off"
          placeholder="账号"
        >
          <svg-icon
            slot="prefix"
            icon-class="user"
            class="el-input__icon input-icon"
          />
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          auto-complete="off"
          placeholder="密码"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon
            slot="prefix"
            icon-class="password"
            class="el-input__icon input-icon"
          />
        </el-input>
      </el-form-item>
      <el-form-item prop="code" v-if="captchaEnabled">
        <el-input
          v-model="loginForm.code"
          auto-complete="off"
          placeholder="验证码"
          style="width: 63%"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon
            slot="prefix"
            icon-class="validCode"
            class="el-input__icon input-icon"
          />
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" class="login-code-img" />
        </div>
      </el-form-item>
      <div class="remember-container">
        <el-checkbox v-model="loginForm.rememberMe">记住密码</el-checkbox>
        <el-link type="primary" @click="showForgotPassword = true" class="forgot-password">找回密码</el-link>
      </div>
      <el-form-item style="width: 100%">
        <el-button
          :loading="loading"
          size="medium"
          type="primary"
          style="width: 100%"
          @click.native.prevent="handleLogin"
        >
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
        <div style="float: right" v-if="register">
          <router-link class="link-type" :to="'/register'"
          >立即注册</router-link
          >
        </div>
      </el-form-item>
    </el-form>

    <!-- 找回密码表单 -->
    <el-form
      v-if="showForgotPassword"
      ref="forgotForm"
      :model="forgotForm"
      :rules="forgotRules"
      class="login-form"
    >
      <h3 class="title">找回密码</h3>
      <el-form-item prop="username">
        <el-input
          v-model="forgotForm.username"
          type="text"
          auto-complete="off"
          placeholder="请输入账号"
        >
          <svg-icon
            slot="prefix"
            icon-class="user"
            class="el-input__icon input-icon"
          />
        </el-input>
      </el-form-item>
      <el-form-item prop="smsCode">
        <el-input
          v-model="forgotForm.smsCode"
          auto-complete="off"
          placeholder="短信验证码"
          style="width: 63%"
        >
          <svg-icon
            slot="prefix"
            icon-class="validCode"
            class="el-input__icon input-icon"
          />
        </el-input>
        <el-button
          :disabled="smsCountdown > 0"
          @click="handleSendSms"
          style="width: 37%; padding: 12px 0"
        >
          {{ smsCountdown > 0 ? `${smsCountdown}秒后重试` : '获取验证码' }}
        </el-button>
      </el-form-item>
      <el-form-item prop="newPassword" v-if="forgotForm.smsCode">
        <el-input
          v-model="forgotForm.newPassword"
          type="password"
          auto-complete="off"
          placeholder="请输入新密码"
          show-password
          @input="handlePasswordInput"
        >
          <svg-icon
            slot="prefix"
            icon-class="password"
            class="el-input__icon input-icon"
          />
        </el-input>
      </el-form-item>
      <el-form-item
        prop="confirmPassword"
        v-if="forgotForm.newPassword"
      >
        <el-input
          v-model="forgotForm.confirmPassword"
          type="password"
          auto-complete="off"
          placeholder="请确认新密码"
          show-password
        >
          <svg-icon
            slot="prefix"
            icon-class="password"
            class="el-input__icon input-icon"
          />
        </el-input>
      </el-form-item>
      <el-form-item style="width: 100%">
        <el-button
          :loading="forgotLoading"
          size="medium"
          type="primary"
          style="width: 100%"
          @click.native.prevent="handleResetPassword"
        >
          <span v-if="!forgotLoading">提 交</span>
          <span v-else>提 交 中...</span>
        </el-button>
        <div style="float: right">
          <el-link type="primary" @click="showForgotPassword = false">返回登录</el-link>
        </div>
      </el-form-item>
    </el-form>

    <!--  底部  -->
    <div class="el-login-footer">
      <span>Copyright © 2025 bjair All Rights Reserved.</span>
    </div>
  </div>
</template>

<script>
import {getCodeImg, getSmsCode, resetPassword} from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";

export default {
  name: "Login",
  data() {
    // 密码强度校验
    const validatePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入新密码"));
      } else if (value.length < 6) {
        callback(new Error("密码长度不能少于6位"));
      } else {
        callback();
      }
    };

    return {
      title: process.env.VUE_APP_TITLE,
      codeUrl: "",
      loginForm: {
        username: "",
        password: "",
        rememberMe: false,
        code: "",
        uuid: "",
      },
      forgotForm: {
        username: "",
        smsCode: "",
        newPassword: "",
        confirmPassword: ""
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入您的账号" },
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" },
        ],
        code: [{ required: true, trigger: "change", message: "请输入验证码" }],
      },
      forgotRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入您的账号" },
        ],
        smsCode: [
          { required: true, trigger: "blur", message: "请输入短信验证码" },
        ],
        newPassword: [
          { validator: validatePassword, trigger: "blur" }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入密码', trigger: 'blur' },
          { validator: this.validateConfirmPassword, trigger: 'blur' }
        ]
      },
      loading: false,
      forgotLoading: false,
      showForgotPassword: false,
      smsCountdown: 0,
      smsTimer: null,
      // 验证码开关
      captchaEnabled: true,
      // 注册开关
      register: true,
      redirect: undefined,
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
    showForgotPassword(val) {
      if (!val) {
        // 重置找回密码表单
        this.$refs.forgotForm && this.$refs.forgotForm.resetFields();
        clearInterval(this.smsTimer);
        this.smsCountdown = 0;
      }
    }
  },
  beforeDestroy() {
    clearInterval(this.smsTimer);
  },
  created() {
    this.getCode();
    this.getCookie();
  },
  methods: {
    getCode() {
      getCodeImg().then((res) => {
        this.captchaEnabled =
          res.captchaEnabled === undefined ? true : res.captchaEnabled;
        if (this.captchaEnabled) {
          this.codeUrl = "data:image/gif;base64," + res.img;
          this.loginForm.uuid = res.uuid;
        }
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get("rememberMe");
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password:
          password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 30 });
            Cookies.set("password", encrypt(this.loginForm.password), {
              expires: 30,
            });
            Cookies.set("rememberMe", this.loginForm.rememberMe, {
              expires: 30,
            });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove("rememberMe");
          }
          this.$store
            .dispatch("Login", this.loginForm)
            .then(() => {
              this.$router.push({ path: this.redirect || "/" }).catch(() => {});
            })
            .catch(() => {
              this.loading = false;
              if (this.captchaEnabled) {
                this.getCode();
              }
            });
        }
      });
    },
    // 发送短信验证码
    async handleSendSms() {
      try {
        await this.$refs.forgotForm.validateField('username');
        const res = await getSmsCode(this.forgotForm.username.trim());
        console.log("res === ", res)
        if (res.code === 200) {
          this.$message.success(res.msg);
          this.startCountdown();
        } else {
          this.$message.error(res.msg || '发送失败');
        }
      } catch (error) {
        this.$message.error('验证码发送异常');
        console.error('短信发送错误:', error);
      }
    },
    startCountdown() {
      this.smsCountdown = 60;
      this.smsTimer = setInterval(() => {
        this.smsCountdown--;
        if (this.smsCountdown <= 0) {
          clearInterval(this.smsTimer);
        }
      }, 1000);
    },
    beforeDestroy() {
      clearInterval(this.smsTimer); // 清理定时器
    },

    // 重置密码
    async handleResetPassword() {
      try {
        const valid = await this.$refs.forgotForm.validate();
        if (!valid) return;

        this.forgotLoading = true;
        const params = {
          username: this.forgotForm.username,
          smsCode: this.forgotForm.smsCode,
          newPassword: this.forgotForm.newPassword,
          confirmPassword: this.forgotForm.confirmPassword
        };

        const res = await resetPassword(params);
        if (res.code === 200) {
          this.$message.success('密码重置成功');
          this.showForgotPassword = false;
        } else {
          this.$message.error(res.msg || '重置失败');
        }
      } catch (error) {
        this.$message.error('请求异常');
        console.error('密码重置错误:', error);
      } finally {
        this.forgotLoading = false;
      }
    },

    // 密码确认
    validateConfirmPassword(rule, value, callback) {
      if (value !== this.forgotForm.newPassword) {
        callback(new Error('两次输入的密码不一致'));
      } else {
        callback();
      }
    },
    handlePasswordInput() {
      // 当新密码变化时重置确认密码字段的验证状态
      this.$refs.forgotForm.validateField('confirmPassword');
    }
  },
};
</script>

<style rel="stylesheet/scss" lang="scss">
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url("../assets/images/login-background.jpg");
  background-size: cover;
}
.title {
  margin: 0px auto 30px auto;
  text-align: center;
  font-weight: bold;
  color: #000828;
}

.login-form {
  border-radius: 6px;
  background: #e9eef4;
  width: 400px;
  padding: 25px 25px 5px 25px;
  z-index: 1;
  .el-input {
    height: 38px;
    input {
      height: 38px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  width: 33%;
  height: 38px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.login-code-img {
  height: 38px;
}
.remember-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}
.forgot-password {
  margin-left: 10px;
}
.password-match-error {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
}
</style>
