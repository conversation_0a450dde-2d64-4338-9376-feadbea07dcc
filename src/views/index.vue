<template>
  <div class="app-container home">
    <div class="banner-container">
      <img src="https://bjqsnai.oss-cn-beijing.aliyuncs.com/file/20250611/jpg/68498d1f8dcc511763d4b560.jpg">
    </div>
    <div class="active-list">
      <div class="item" v-for="(item, index) in activeList" :key="item.value">
        <div class="top">活动{{ index + 1 }}：{{ item.label }}</div>
        <div class="content">
          <div class="tex3">{{ item.text3 }}</div>
          <p></p>
          <div class="tex1">{{ item.text1 }}</div>
          <div class="tex2">{{ item.text2 }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Index",
  data() {
    return {
      activeList: [
        { label: 'AI艺术生成',
          value: 1,
          text1: '传统文化的"创新创造"',
          text3: '参与对象：小学，参与人数：个人',
          text2: '生成式人工智能（AIGC）在生成艺术作品方面具有强大的能力和广泛的应用。鼓励学生运用AI和AIGC技术推动中华优秀传统文化的创造性转化与创新性发展，以新颖视角和前沿手段，挖掘并焕新中国文化的深厚内涵与独特魅力' },
        { label: 'AI交互设计',
          value: 2,
          text1: '"对话"中华文明',
          text3: '参与对象：普通中学/中专/职高在校学生，参与人数：个人或≤3人团队',
          text2: '本活动主要突出人工智能时代人机交互方式的变革，从需求分析、界面设计到功能实现，增强AI软件项目落地实践的能力' },
        { label: 'AI工程实践',
          value: 3,
          text1: '生活中的机器助手',
          text3: '参与对象：普通中学/中专/职高在校学生，参与人数：个人或≤3人团队',
          text2: '帮助青少年掌握基础硬件知识，如传感器原理、单片机使用等，同时了解AI算法在硬件中的应用，在动手实践过程中，提升工程设计、问题解决和团队协作能力，培养创新思维与实践精神' },
        { label: 'AI算法挑战',
          value: 4,
          text1: '数智北京',
          text3: '参与对象：高校本科及硕士研究生，参与人数：个人或≤3人团队',
          text2: '鼓励参与者突破现有算法局限，开发更高效、更具创新性的AI算法，围绕智慧城市建设中的数智治理、数智乡村、公共文化服务、休闲旅游、文物保护、商业等场景，用算法解决实际问题，探索AI在多学科领域的新应用、新场景，为行业技术进步提供新的思路和方向' },
        { label: 'AI创新教学案例征集',
          value: 5,
          text1: '数智教育',
          text3: '参与对象：小学、初中、高中、（含中专、职高）的一线教师，科技馆和少年宫等公益校外机构教师，所属学段以第一作者所在单位划分，参与人数：个人或≤2人团队',
          text2: '鼓励教师探索人工智能与教学深度融合的创新路径，开发具有示范价值的教学案例，提升教师运用AI技术开展教学的能力。基于人工智能技术开展跨学科主题项目，如AI+传统文化、AI+环保等，培养学生综合素养与创新能力' },
      ],

    }
  }
}
</script>

<style scoped lang="scss">
.home {
  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;
  height: 100vh;
  display: flex;
  flex-direction: column;

  .banner-container {
    width: 100%;
    height: auto;
    overflow: visible;
    background-color: #000; /* 添加深色背景 */
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
  }

  img {
    display: block;
    max-width: 100%;
    height: auto;
    margin: 0 auto;
  }

  .active-list {
    display: flex;
    flex-wrap: nowrap;
    column-gap: 24px;
    row-gap: 24px;
    padding: 24px 0px 24px;
    justify-content: space-between;

    .item {
      // min-width: 300px;
      background: linear-gradient(180deg, #E9FBFF 0%, #FFFFFF 100%);
      box-shadow: 0px 0px 17px 0px rgba(169, 190, 214, 0.17);
      min-height: 200px;
      display: flex;
      flex-direction: column;
      align-items: center;
      row-gap: 12px;
      padding: 24px;
      cursor: pointer;
      border-radius: 8px;

      .top {
        font-size: 20px;
        font-weight: bold;
      }

      .content {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        row-gap: 12px;

        //.tex1 {
        //  font-size: 18px;
        //  font-weight: 500;
        //}
        //
        //.tex2 {
        //  font-size: 16px;
        //}
        //
        //.tex3 {
        //  font-size: 17px;
        //}

        .tex3 {
          font-size: 14px;
          color: #666;
          font-style: italic;
          margin-bottom: 8px;
        }

        .tex1 {
          font-size: 16px;
          font-weight: 500;
          color: #001f3f;
          margin-bottom: 6px;
        }

        .tex2 {
          font-size: 14px;
          line-height: 1.5;
          color: #333;
        }
      }
    }
  }
}
</style>
