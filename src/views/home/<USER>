<template>
  <div id="app" class="aigc-page">
    <!-- 顶部导航栏 -->
    <el-header class="nav-bar">
      <div class="nav-left">
        <img src="@/assets/home/<USER>" style="height: 50px"/>
      </div>

      <!-- 右侧：导航项 + 登录/头像 -->
      <div class="nav-right">
        <div
          class="nav-item"
          :class="{ active: current === 'active1' }"
          @click="
    current = 'active1';
    scrollToSection('active1');
  "
        >
          活动主页
        </div>
        <div
          class="nav-item"
          :class="{ active: current === 'active2' }"
          @click="
    current = 'active2';
    scrollToSection('intro');
  "
        >
          活动简介
        </div>
        <div
          class="nav-item"
          :class="{ active: current === 'active3' }"
          @click="
    current = 'active3';
    scrollToSection('guide');
  "
        >
          活动指南
        </div>
        <div
          class="nav-item"
          :class="{ active: current === 'active4' }"
          @click="
    current = 'active4';
    scrollToSection('time-plan');
  "
        >
          时间安排
        </div>
        <div
          class="nav-item"
          :class="{ active: current === 'active5' }"
          @click="
    current = 'active5';
    scrollToSection('dongtai');
  "
        >
          活动通知
        </div>

        <!-- 登录或头像 -->
        <div v-if="!isLoggedIn" class="nav-login" @click="goLogin">登录</div>

        <el-dropdown v-else @command="handleCommand">
          <span class="el-dropdown-link">
            <el-avatar :size="32" :src="avatar"/>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="profile">报名</el-dropdown-item>
            <el-dropdown-item command="logout" @click.native="logout"
            >退出登录
            </el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </el-header>

    <!-- Banner -->
    <div class="banner-container">
      <img src="https://bjqsnai.oss-cn-beijing.aliyuncs.com/file/20250611/jpg/68498d1f8dcc511763d4b560.jpg" class="bg-img"/>
    </div>

    <!-- 活动简介 -->
    <section class="section intro" id="intro">
      <img src="@/assets/home/<USER>" class="jianjie-img"/>
      <p>
        为贯彻落实国务院《新一代人工智能发展规划》和教育部等十八部门《关于加强新时代中小学科学教育工作的意见》等文件精神，进一步在教育"双减"中做好科学教育加法，向广大青少年普及人工智能科技，提高智能思维和解决问题能力，培养具备创新精神的人工智能后备人才，集中展示年度活动中涌现出的青少年人工智能创新实践优秀作品，搭建青少年、科技教师与高校专家、科技社团青年、人工智能科技企业创业者之间的交流平台，培养人工智能领域具备科研潜质和应用实践能力的创新型人才，促进青少年人工智能创新实践教育高质量普及开展，北京市科学技术协会和北京市门头沟区人民政府联合举办2025年北京青少年人工智能创新实践交流展示活动。
      </p>
      <p>
        本次活动旨在鼓励 AI
        创新内容创作，创作类别有：AI艺术生成、AI交互设计、AI工程实践、AI算法挑战、AI创新教学案例征集。
      </p>
    </section>

    <!-- 活动指南 -->
    <section class="section guide" id="guide">
      <img src="@/assets/home/<USER>" class="zhinan-img"/>
<!--      <div class="section-title">活动指南</div>-->
    </section>
    <section class="active-list">
      <div
        v-for="(item, index) in activeList"
        :key="item.value"
        @click="openActivity(item)"
        class="item"
      >
        <!-- 新增图片 -->
        <div class="activity-image-container">
          <img
            :src="require(`@/assets/home/<USER>/${getFilenameByLabel(item.label)}.png`)"
            :alt="item.label"
            class="activity-icon"
          />
        </div>
        <div class="top">活动{{ index + 1 }}：{{ item.label }}</div>
        <div class="summary">
          <div class="tex3">{{ item.text3 }}</div>
          <p></p>
          <div class="tex1">{{ item.text1 }}</div>
          <div class="tex2">{{ item.text2 }}</div>
        </div>
      </div>
    </section>

    <!-- 优秀作品 -->
    <!--    <section class="section works" id="zuopin">-->
    <!--      <img src="@/assets/home/<USER>" class="jianjie-img"/>-->
    <!--      <el-row :gutter="20">-->
    <!--        <el-col :span="8" v-for="work in works" :key="work.title">-->
    <!--          <el-card-->
    <!--            :body-style="{ padding: '10px' }"-->
    <!--            @click.native="openDialog(work)"-->
    <!--          >-->
    <!--            <img :src="work.image" class="work-img"/>-->
    <!--            <div>{{ work.title }}</div>-->
    <!--          </el-card>-->
    <!--        </el-col>-->
    <!--      </el-row>-->
    <!--    </section>-->

    <!-- 时间安排 -->
    <section class="section time-plan" id="time-plan">
      <img src="@/assets/home/<USER>" class="anpai-img"/>
      <div class="time-schedule-container">
        <div class="time-item">
          <img src="@/assets/home/<USER>/time1.png" class="time-img"/>
          <div class="time-info">
            <div class="time-date">2025年5月</div>
            <div class="time-desc">前期筹备组织阶段，发布活动指南，明确作品要求及评价维度</div>
          </div>
        </div>
        <div class="time-item">
          <img src="@/assets/home/<USER>/time2.png" class="time-img"/>
          <div class="time-info">
            <div class="time-date">2025年6月-7月</div>
            <div class="time-desc">作品申报和初评活动组织阶段，组织项目申报、资格审查、线上初评</div>
          </div>
        </div>
        <div class="time-item">
          <img src="@/assets/home/<USER>/time3.png" class="time-img"/>
          <div class="time-info">
            <div class="time-date">2025年8月-10月</div>
            <div class="time-desc">终评活动组织阶段，举办线下优秀作品成果展示、AI创新教学案例交流</div>
          </div>
        </div>
        <div class="time-item">
          <img src="@/assets/home/<USER>/time4.png" class="time-img"/>
          <div class="time-info">
            <div class="time-date">2025年11月-12月</div>
            <div class="time-desc">总结阶段，总结研讨，编制下一届活动指南等</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 活动通知 -->
<!--    <section class="section guide" id="guide">-->
<!--      <img src="@/assets/home/<USER>" class="tongzhi-img"/>-->
<!--      &lt;!&ndash;      <div class="section-title">活动指南</div>&ndash;&gt;-->
<!--    </section>-->
    <section id="dongtai" class="section news">
      <img src="@/assets/home/<USER>" class="tongzhi-img"/>
      <div class="process-container">
<!--        <div class="process-header">活动通知</div>-->
        <div class="process-list">
          <div class="process-item" v-for="(item, index) in news" :key="index">
            <div class="process-icon">
              <el-icon>
                <calendar/>
              </el-icon>
            </div>
            <div class="process-content">
              <div class="process-date">{{ item.date }}</div>
              <a :href="item.link" class="process-text" target="_blank" rel="noopener">{{ item.text }}</a>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
      <div>主办单位：北京市科学技术协会  北京市教育委员会
      共青团北京市委员会  北京市门头沟区人民政府</div>
      <div>联系方式：<EMAIL></div>
    </footer>

    <el-dialog
      :visible.sync="dialogVisible"
      width="60%"
      :show-close="true"
      :close-on-click-modal="true"
      :modal-append-to-body="true"
      :destroy-on-close="true"
      class="video-dialog"
      @open="handleOpen"
      @close="handleClose"
    >
      <div class="video-container">
        <!-- 关闭按钮，浮在右上角 -->
        <div class="custom-close-btn" @click="closeDialog">关闭</div>
        <video
          ref="videoPlayer"
          class="video-player"
          v-if="selectedWork.video"
          :src="selectedWork.video"
          controls
          style="width: 100%; height: auto; display: block"
        ></video>
      </div>
    </el-dialog>

    <el-dialog
      :visible.sync="showDialog"
      width="45%"
      :title="currentActivity.label"
    >
      <div v-if="loading" class="loading">
        <i class="el-icon-loading"></i> 加载中...
      </div>
      <div
        v-else
        class="markdown-content"
        v-html="activityContent"
      ></div>
    </el-dialog>

  </div>
</template>

<script>
import axios from "axios";
import Cookies from "js-cookie";
import A from "@/assets/home/<USER>";
import B from "@/assets/home/<USER>";
import C from "@/assets/home/<USER>";
import {loadActivityContent} from '@/utils/activityLoader'

export default {
  data() {
    return {
      avatar: require("@/assets/home/<USER>"),
      current: "",
      isLoggedIn: false,
      rewards: [
        {rank: "一等奖", amount: 50000},
        {rank: "二等奖", amount: 20000},
        {rank: "三等奖", amount: 10000},
      ],
      topics: [
        {name: "视频创作", desc: "结合AIGC模型进行创意视频内容输出"},
        {name: "图片生成", desc: "图文创作或海报设计"},
      ],
      guideSteps: [
        "了解大赛主题及方向",
        "选择合适的工具",
        "结合实际场景进行创作",
        "提交作品并填写信息",
      ],
      tools: [
        {name: "BlueLM", desc: "大语言模型平台"},
        {name: "多模态工具", desc: "图片、视频、语音生成工具"},
        {name: "模型训练平台", desc: "支持大模型微调"},
      ],
      works: [
        {
          title: "作品1",
          image: A,
          video:
            "https://aigc-fs.vivo.com.cn/cf-aigc-n/1%E3%80%81Weaver-%E7%BC%96%E7%BB%87%E7%94%9F%E6%B4%BB%EF%BC%8C%E4%BB%A5%E6%98%9F%E4%B8%BA%E5%9B%BE.mp4",
        },
        {
          title: "作品2",
          image: B,
          video:
            "https://aigc-fs.vivo.com.cn/cf-aigc-n/2%E3%80%81%E7%BB%98%E6%B8%B8%E6%88%8F%E2%80%94%E2%80%94%E5%9F%BA%E4%BA%8EAI%E6%99%BA%E8%83%BD%E4%BD%93%E7%9A%84%E5%8F%AF%E4%BA%A4%E4%BA%92%E5%BC%8F%E7%BB%98%E7%94%BB%E6%95%99%E8%82%B2%E6%B8%B8%E6%88%8F.mp4",
        },
        {
          title: "作品3",
          image: C,
          video:
            "https://aigc-fs.vivo.com.cn/cf-aigc-n/3%E3%80%81%E8%93%9D%E7%B2%BE%E7%81%B5%E4%BC%98%E6%B5%8B%20Smurfs%E2%80%94%E2%80%94%E6%96%B0%E6%A6%82%E5%BF%B5%E4%BD%93%E9%AA%8C%E6%B5%8B%E8%AF%95%E8%BE%85%E5%8A%A9%E5%B9%B3%E5%8F%B0.mp4",
        },
      ],
      news: [
        {
          // date: "《2025年北京青少年人工智能创新实践活动指南》",
          text: "《2025年北京青少年人工智能创新实践活动指南》",
          link: "https://www.bast.net.cn/art/2025/5/20/art_31266_28101.html"
        },
        {
          // date: "《关于开展2025年北京青少年人工智能创新实践活动的通知》",
          text: "《关于开展2025年北京青少年人工智能创新实践活动的通知》",
          link: "https://www.bast.net.cn/art/2025/3/3/art_31266_27572.html"
        },
      ],
      activeList: [
        {
          label: "AI艺术生成",
          value: 1,
          text1: "传统文化的\"创新创造\"",
          text3: '参与对象：小学，参与人数：个人',
          text2:
            "生成式人工智能（AIGC）在生成艺术作品方面具有强大的能力和广泛的应用。鼓励学生运用AI和AIGC技术推动中华优秀传统文化的创造性转化与创新性发展，以新颖视角和前沿手段，挖掘并焕新中国文化的深厚内涵与独特魅力",
          pdfUrl: "https://bjqsnai.oss-cn-beijing.aliyuncs.com/file/20250607/pdf/6844440f8dcc359a902506e4.pdf"
        },
        {
          label: "AI交互设计",
          value: 2,
          text1: "\"对话\"中华文明",
          text3: '参与对象：普通中学/中专/职高在校学生，参与人数：个人或≤3人团队',
          text2:
            "本活动主要突出人工智能时代人机交互方式的变革，从需求分析、界面设计到功能实现，增强AI软件项目落地实践的能力",
          pdfUrl: "https://bjqsnai.oss-cn-beijing.aliyuncs.com/file/20250607/pdf/684443e68dcc359a902506e2.pdf"
        },
        {
          label: "AI工程实践",
          value: 3,
          text1: "生活中的机器助手",
          text3: '参与对象：普通中学/中专/职高在校学生，参与人数：个人或≤3人团队',
          text2:
            "帮助青少年掌握基础硬件知识，如传感器原理、单片机使用等，同时了解AI算法在硬件中的应用，在动手实践过程中，提升工程设计、问题解决和团队协作能力，培养创新思维与实践精神",
          pdfUrl: "https://bjqsnai.oss-cn-beijing.aliyuncs.com/file/20250607/pdf/684443ba8dcc359a902506e1.pdf"
        },
        {
          label: "AI算法挑战",
          value: 4,
          text1: "数智北京",
          text3: '参与对象：高校本科及硕士研究生，参与人数：个人或≤3人团队',
          text2:
            "鼓励参与者突破现有算法局限，开发更高效、更具创新性的AI算法，围绕智慧城市建设中的数智治理、数智乡村、公共文化服务、休闲旅游、文物保护、商业等场景，用算法解决实际问题，探索AI在多学科领域的新应用、新场景，为行业技术进步提供新的思路和方向",
          pdfUrl: "https://bjqsnai.oss-cn-beijing.aliyuncs.com/file/20250607/pdf/684443fb8dcc359a902506e3.pdf"
        },
        {
          label: "AI创新教学案例征集",
          value: 5,
          text1: "数智教育",
          text3: '参与对象：小学、初中、高中、（含中专、职高）的一线教师，科技馆和少年宫等公益校外机构教师，所属学段以第一作者所在单位划分，参与人数：个人或≤2人团队',
          text2:
            "鼓励教师探索人工智能与教学深度融合的创新路径，开发具有示范价值的教学案例，提升教师运用AI技术开展教学的能力。基于人工智能技术开展跨学科主题项目，如AI+传统文化、AI+环保等，培养学生综合素养与创新能力",
          pdfUrl: "https://bjqsnai.oss-cn-beijing.aliyuncs.com/file/6843b96d8dcc83d70fd54aab.pdf"
        },
      ],
      dialogVisible: false,
      selectedPdfUrl: "",
      selectedWork: {},
      video: null,
      showDialog: false,
      loading: false,
      currentActivity: {},
      activityContent: ''
    };
  },
  mounted() {
    const token = sessionStorage.getItem("Admin-Token");
    this.isLoggedIn = token ? true : false;
  },
  watch: {
    $route(to) {
      // 只要进入本页面，就执行初始化（无论是不是同一路径）
      if (to.path === this.$route.path) {
        const token = sessionStorage.getItem("Admin-Token");
        this.isLoggedIn = token ? true : false;
      }
    },
    isLoggedIn(newVal) {
      console.log("登录状态变化:", newVal);
    },
  },
  methods: {
    // scrollToSection(id) {
    //   const el = document.getElementById(id);
    //   wwwwwwwwwwwwwww;
    //   if (el) {
    //     el.scrollIntoView({behavior: "smooth"}); // 平滑滚动
    //   }
    // },
    scrollToSection(id) {
      if (id === 'active1') {
        const bannerEl = document.querySelector('.banner-container');
        if (bannerEl) {
          bannerEl.scrollIntoView({behavior: 'smooth'});
        }
      } else if (id === 'app') {
        const activeListEl = document.querySelector('.active-list');
        if (activeListEl) {
          activeListEl.scrollIntoView({behavior: 'smooth', block: 'start'});
        }
      } else {
        const el = document.getElementById(id);
        if (el) {
          el.scrollIntoView({behavior: 'smooth'});
        }
      }
    },

    openDialog(work) {
      return;
      this.selectedWork = work;
      this.dialogVisible = true;
    },
    handleOpen() {
      // 打开后播放视频
      this.$nextTick(() => {
        const video = this.$refs.videoPlayer;
        if (video) video.play();
      });
    },
    closeDialog() {
      this.dialogVisible = false;
    },
    handleClose() {
      const video = this.$refs.videoPlayer;
      console.log(111);
      console.log(video);
      if (video) {
        video.pause(); // 暂停播放
        video.currentTime = 0; // 回到开头
      }
    },
    goLogin() {
      if (this.$route.path !== "/login") {
        this.$router.push("/login");
      }
    },
    goAbout() {
      this.$router.push({path: "/groupPage"});
    },
    handleCommand(command) {
      if (command === "profile") {
        this.$router.push("/index");
      } else if (command === "logout") {
        this.logout(); // 调用退出函数
      }
    },
    logout() {
      this.$confirm("确定注销并退出系统吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$store.dispatch("LogOut").then(() => {
            location.href = "/home";
          });
        })
        .catch(() => {
        });
    },
    getFilenameByLabel(label) {
      const map = {
        'AI艺术生成': 'ai-art',
        'AI交互设计': 'ai-design',
        'AI工程实践': 'ai-eng',
        'AI算法挑战': 'ai-algo',
        'AI创新教学案例征集': 'ai-edu'
      };
      return map[label] || null;
    },

    async openActivity(item) {
      this.currentActivity = item;
      this.showDialog = true;
      this.loading = true;

      try {
        const filename = this.getFilenameByLabel(item.label);  // 使用 this 调用
        if (!filename) throw new Error('未知活动类型');
        this.activityContent = await loadActivityContent(filename);
      } catch (error) {
        this.activityContent = '<p>内容加载失败，请刷新重试</p>';
        console.error('加载活动内容失败:', error);
      } finally {
        this.loading = false;
      }
    }

  },
};
</script>

<style scoped lang="scss">
/* 激活状态，加下划线 */
.nav-item .active {
  border-bottom: 2px solid white;
  color: white;
}

.video-container {
  position: relative;
  width: 100%;
}

/* 视频本身 */
.video-player {
  width: 100%;
  height: auto;
  background: black;
  border-radius: 0;
}

/* 自定义关闭按钮 */
.custom-close-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  color: white;
  background-color: rgba(0, 0, 0, 0.6);
  padding: 5px 10px;
  font-size: 16px;
  border-radius: 4px;
  cursor: pointer;
  z-index: 10;
  transition: background-color 0.3s;
}

.custom-close-btn:hover {
  background-color: rgba(255, 0, 0, 0.7);
}

.video-dialog > > > .el-dialog {
  padding: 0;
  background: transparent;
  box-shadow: none;
}

.video-dialog > > > .el-dialog__header {
  display: none;
  /* 去掉标题 */
}

.video-dialog > > > .el-dialog__body {
  padding: 0;
  background: transparent;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.video-dialog > > > video {
  display: block;
  width: 100%;
  height: auto;
  border-radius: 0 !important;
  background: black;
  /* 防止加载中显示白底 */
}

video {
  border-radius: 0 !important;
  /* 可能影响内部内容的视觉 */
}

.jianjie-img {
  width: 314px;
  height: 85px;
  margin: 0 auto 52.41px;
  display: block;
}

.zhinan-img {
  width: 314px;
  height: 85px;
  margin: 0 auto 10px;
  display: block;
}

.anpai-img {
  width: 314px;
  height: 85px;
  margin: 0 auto 52.41px;
  display: block;
}

.anpai-zhanwei-img {
  width: auto;
  height: auto;
  margin: 0 auto 20.41px;
  display: block;
}

.tongzhi-img {
  width: 314px;
  height: 85px;
  margin: 0 auto 52.41px;
  display: block;
}

.shezhi-img {
  width: 314px;
  height: 60px;
  margin: 0 auto 52.41px;
  display: block;
}

.bg-img {
  width: 100%;
  height: 100%;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  display: flex;
  align-items: center;
  padding-left: 5%;
  box-sizing: border-box;
}

.overlay .title-1 {
  font-size: clamp(10px, 2vw, 28px);
  line-height: 1.5;
  color: #fffffff5;
}

.overlay .title-2 {
  font-size: clamp(16px, 10vw, 50px);
  font-weight: 750;
  line-height: 1.5;
  color: #fff;
}

.el-menu--horizontal.el-menu {
  border-bottom: 0;
}

.bg-img {
  width: 100%;
  display: block;
}

img {
  overflow-clip-margin: content-box;
  overflow: clip;
}

.aigc-page {
  font-family: "Helvetica Neue", sans-serif;
  color: #333;
}

.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background: #001f3f;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-right {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.nav-item {
  cursor: pointer;
  font-size: 16px;
  transition: color 0.2s;
  margin-right: 40px;
}

.banner {
  background: linear-gradient(to right, #0052d9, #1890ff);
  /* background-image: url('@assets/home/<USER>'); */
  color: white;
  text-align: center;
  /* padding: 60px 20px; */
}

.section {
  padding: 60px 20px;
  background: #f9f9f9;
  // margin-bottom: 20px;
}

.active-list {
  background: #f9f9f9;
  display: flex;
  flex-wrap: wrap;
  column-gap: 24px;
  row-gap: 24px;
  padding: 12px 24px 24px;
  justify-content: space-around;

  .item {
    width: 330px;
    background: linear-gradient(180deg, #e9fbff 0%, #ffffff 100%);
    box-shadow: 0px 0px 17px 0px rgba(169, 190, 214, 0.17);
    min-height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    row-gap: 12px;
    padding: 24px;
    cursor: pointer;
    border-radius: 8px;

    .top {
      font-size: 20px;
      font-weight: bold;
    }

    .summary {
      width: 100%;

      .tex3 {
        font-size: 14px;
        color: #666;
        font-style: italic;
        margin-bottom: 8px;
      }

      .tex1 {
        font-size: 16px;
        font-weight: 500;
        color: #001f3f;
        margin-bottom: 6px;
      }

      .tex2 {
        font-size: 14px;
        line-height: 1.5;
        color: #333;
      }
    }
  }
}

.footer {
  background: #001f3f;
  color: #fff;
  padding: 30px;
  text-align: center;
}

.work-img {
  width: 100%;
  height: 450px;
  margin-bottom: 10px;
}

.nav-login {
  cursor: pointer;
}

.process-container {
  max-width: 800px;
  margin: 0 auto;
  background: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.process-header {
  background: #001f3f;
  color: white;
  padding: 12px 20px;
  border-radius: 6px;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 20px;
  text-align: center;
}

.process-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.process-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  background: white;
  padding: 15px;
  border-radius: 6px;
  transition: all 0.3s ease;
  border-left: 4px solid #001f3f;
}

.process-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 31, 63, 0.1);
}

.process-icon {
  color: #001f3f;
  font-size: 20px;
  margin-top: 2px;
}

.process-content {
  flex: 1;
}

.process-date {
  color: #001f3f;
  font-weight: bold;
  margin-bottom: 5px;
}

.process-text {
  color: #333;
  text-decoration: none;
  display: inline-block;
  transition: color 0.2s;
}

.process-text:hover {
  color: #001f3f;
  text-decoration: underline;
}

.section {
  padding: 60px 20px;
  background: #f9f9f9;

  &.guide {
    text-align: center;
  }
}

.section-title {
  font-size: 24px;
  font-weight: bold;
  color: white;
  background-color: #001f3f;
  display: inline-block;
  padding: 10px 30px;
  border-radius: 8px;
  margin-bottom: 40px;
}

.pdf-dialog > > > .el-dialog__body {
  padding: 0;
}

.pdf-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 600px;
  background-color: #f5f5f5;
}

/* 活动列表样式 */
.activity-item {
  cursor: pointer;
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 8px;
  background: linear-gradient(180deg, #e9fbff 0%, #ffffff 100%);
  box-shadow: 0px 0px 17px 0px rgba(169, 190, 214, 0.17);
}

.markdown-content {
  max-height: 60vh;
  overflow-y: auto;
  padding: 0 10px;
  line-height: 1.8;
}

.markdown-content h2 {
  color: #001f3f;
  margin: 25px 0 15px 0;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.loading {
  text-align: center;
  padding: 30px;
  color: #666;
}

.active-list .item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 24px;
  cursor: pointer;
  border-radius: 8px;
  transition: transform 0.2s ease;
  width: 330px;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.activity-image-container {
  margin-bottom: 12px;
}

.activity-icon {
  width: 240px;
  height: 240px;
  object-fit: contain;
  border-radius: 8px;
  transition: transform 0.3s ease;
}

.item:hover .activity-icon {
  transform: scale(1.1);
}

.time-schedule-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 30px;
  margin-top: 20px;
}

.time-item {
  width: 280px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #ffffff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.time-item:hover {
  transform: translateY(-5px);
}

.time-img {
  width: 100%;
  height: 180px;
  object-fit: contain;
  margin-bottom: 15px;
  border-radius: 4px;
}

.time-info {
  width: 100%;
  text-align: center;
}

.time-date {
  font-size: 18px;
  font-weight: bold;
  color: #001f3f;
  margin-bottom: 8px;
}

.time-desc {
  font-size: 14px;
  color: #555;
  line-height: 1.5;
}

</style>
