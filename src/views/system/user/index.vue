<template>
  <div class="app-container">
    <el-row :gutter="20">
      <splitpanes :horizontal="this.$store.getters.device === 'mobile'" class="default-theme">
        <!--组织数据-->
        <pane size="16">
          <el-col>
            <div class="head-container">
              <el-input v-model="deptName" placeholder="请输入组织名称" clearable size="small" prefix-icon="el-icon-search"
                style="margin-bottom: 20px" />
            </div>
            <div class="head-container">
              <el-tree :data="deptOptions" :props="defaultProps" :expand-on-click-node="false"
                :filter-node-method="filterNode" ref="tree" node-key="id" default-expand-all highlight-current
                @node-click="handleNodeClick" />
            </div>
          </el-col>
        </pane>
        <!--用户数据-->
        <pane size="84">
          <el-col>
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
              label-width="68px">
              <el-form-item label="用户名称" prop="userName">
                <el-input v-model="queryParams.userName" placeholder="请输入用户名称" clearable style="width: 240px"
                  @keyup.enter.native="handleQuery" />
              </el-form-item>
              <el-form-item label="手机号码" prop="phonenumber">
                <el-input v-model="queryParams.phonenumber" placeholder="请输入手机号码" clearable style="width: 240px"
                  @keyup.enter.native="handleQuery" />
              </el-form-item>
              <el-form-item label="状态" prop="status">
                <el-select v-model="queryParams.status" placeholder="用户状态" clearable style="width: 240px">
                  <el-option v-for="dict in dict.type.sys_normal_disable" :key="dict.value" :label="dict.label"
                    :value="dict.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="创建时间">
                <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
                  range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>

            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                  v-hasPermi="['system:user:add']">新增</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
                  v-hasPermi="['system:user:edit']">修改</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
                  @click="handleDelete" v-hasPermi="['system:user:remove']">删除</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport"
                  v-hasPermi="['system:user:import']">导入</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
                  v-hasPermi="['system:user:export']">导出</el-button>
              </el-col>
              <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
            </el-row>

            <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="50" align="center" />
              <el-table-column label="用户编号" align="center" key="userId" prop="userId" v-if="columns[0].visible" />
              <el-table-column label="用户名称" align="center" key="userName" prop="userName" v-if="columns[1].visible"
                :show-overflow-tooltip="true" />
              <el-table-column label="用户昵称" align="center" key="nickName" prop="nickName" v-if="columns[2].visible"
                :show-overflow-tooltip="true" />
              <el-table-column label="组织" align="center" key="deptName" prop="dept.deptName" v-if="columns[3].visible"
                :show-overflow-tooltip="true" />
              <el-table-column label="手机号码" align="center" key="phonenumber" prop="phonenumber"
                v-if="columns[4].visible" width="120" />
              <el-table-column label="状态" align="center" key="status" v-if="columns[5].visible">
                <template slot-scope="scope">
                  <el-switch v-model="scope.row.status" active-value="0" inactive-value="1"
                    @change="handleStatusChange(scope.row)"></el-switch>
                </template>
              </el-table-column>
              <el-table-column label="创建时间" align="center" prop="createTime" v-if="columns[6].visible" width="160">
                <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.createTime) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="260" class-name="small-padding fixed-width">
                <template slot-scope="scope" v-if="scope.row.userId !== 1">
                  <el-button size="mini" type="text" icon="el-icon-postcard" @click="handleSendEmail(scope.row)"
                    v-hasPermi="['system:user:edit']">邮件</el-button>
                  <el-button size="mini" type="text" icon="el-icon-message" @click="handleSendMessage(scope.row)"
                    v-hasPermi="['system:user:edit']">短信</el-button>
                  <!--                  <el-button size="mini" type="text" icon="el-icon-message" @click="handleSendMessage(scope.row)" v-hasPermi="['system:user:edit']">短信</el-button>-->
                  <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                    v-hasPermi="['system:user:edit']">修改</el-button>
                  <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                    v-hasPermi="['system:user:remove']">删除</el-button>
                  <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)"
                    v-hasPermi="['system:user:resetPwd', 'system:user:edit']">
                    <el-button size="mini" type="text" icon="el-icon-d-arrow-right">更多</el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item command="handleResetPwd" icon="el-icon-key"
                        v-hasPermi="['system:user:resetPwd']">重置密码</el-dropdown-item>
                      <el-dropdown-item command="handleAuthRole" icon="el-icon-circle-check"
                        v-hasPermi="['system:user:edit']">分配角色</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </template>
              </el-table-column>
            </el-table>

            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize" @pagination="getList" />
          </el-col>
        </pane>
      </splitpanes>
    </el-row>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="880px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-position="top">
        <el-row :gutter="32">
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" label="用户名称" prop="userName">
              <el-input v-model="form.userName" placeholder="请输入用户名称" maxlength="30" />
            </el-form-item>
            <el-form-item label="身份证号" prop="IDNumber">
              <el-input v-model="form.IDNumber" placeholder="请输入身份证号" />
            </el-form-item>
            <el-form-item label="用户性别">
              <el-select v-model="form.sex" placeholder="请选择性别">
                <el-option v-for="dict in dict.type.sys_user_sex" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-if="form.userId == undefined" label="用户密码" prop="password">
              <el-input v-model="form.password" placeholder="请输入用户密码" type="password" maxlength="20" show-password />
            </el-form-item>

            <el-form-item label="学校名称" prop="school">
              <el-input v-model="form.school" placeholder="请输入学校名称" maxlength="50" />
            </el-form-item>
            <el-form-item label="学校邮箱" prop="schoolPostalCode">
              <el-input v-model="form.schoolPostalCode" placeholder="请输入学校邮箱" maxlength="50" />
            </el-form-item>
            <el-form-item label="电子邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入电子邮箱" maxlength="50" />
            </el-form-item>
            <el-form-item label="邮件地址" prop="mailingAddress">
              <el-input v-model="form.mailingAddress" placeholder="请输入邮件地址" />
            </el-form-item>
            <el-form-item label="年级" prop="grade">
              <el-select v-model="form.grade" placeholder="请选择年级">
                <el-option v-for="dict in dict.type.grade" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="监护人电话" prop="guardianPhone">
              <el-input v-model="form.guardianPhone" placeholder="请输入手机号码" maxlength="11" />
            </el-form-item>
            <el-form-item label="活动同意书" prop="fileUrl">
              <FileUpload :fileSize="20" :limit="1" :value="form.fileUrl" :isShowTip="false" :fileType="['doc', 'docx']"
                @input="handleChangeFile($event, 'fileUrl')" />
              <el-button type="text" :disabled="false" @click="importTemplateFile">下载模版</el-button>
            </el-form-item>

          </el-col>
          <el-col :span="12">
            <el-form-item label="姓名" prop="nickName">
              <el-input v-model="form.nickName" placeholder="请输入姓名" maxlength="10" />
            </el-form-item>
            <el-form-item label="手机号码" prop="phonenumber">
              <el-input v-model="form.phonenumber" placeholder="请输入手机号码" maxlength="11" />
            </el-form-item>
            <el-form-item label="出生年月" prop="dateOfBirth">
              <el-date-picker
                v-model="form.dateOfBirth"
                type="month"
                placeholder="选择日期"
                value-format="yyyy-MM"
                format="yyyy年MM月">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="角色">
              <el-select v-model="form.roleIds" multiple placeholder="请选择角色">
                <el-option v-for="item in roleOptions" :key="item.roleId" :label="item.roleName" :value="item.roleId"
                  :disabled="item.status == 1"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="学校地址" prop="schoolAddress">
              <el-input v-model="form.schoolAddress" placeholder="请输入学校地址" />
            </el-form-item>
            <el-form-item label="学校电话" prop="schoolPhone">
              <el-input v-model="form.schoolPhone" placeholder="请输入学校电话" maxlength="11" />
            </el-form-item>
            <el-form-item label="邮件地址邮政编码" prop="mailingPostalCode">
              <el-input v-model="form.mailingPostalCode" placeholder="请输入邮件地址邮政编码" />
            </el-form-item>
            <el-form-item label="民族" prop="nationality">
              <el-select v-model="form.nationality" placeholder="请选择民族">
                <el-option v-for="item in nationOptions" :key="item.label" :label="item.label"
                  :value="item.label"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="学校负责人" prop="schoolManager">
              <el-input v-model="form.schoolManager" placeholder="请输入学校负责人" maxlength="10" />
            </el-form-item>
            <el-form-item label="监护人" prop="guardian">
              <el-input v-model="form.guardian" placeholder="请输入监护人" maxlength="10" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline"
            @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 发送邮件对话框 -->
    <el-dialog title="发送邮件" :visible.sync="emailDialog.open" width="650px" append-to-body>
      <el-form ref="emailForm" :model="emailDialog.form" label-width="80px">
        <el-form-item label="邮箱地址" prop="to">
          <el-input v-model="emailDialog.form.to" placeholder="请输入邮箱地址" :disabled="true" />
        </el-form-item>
        <el-form-item label="邮件主题" prop="subject">
          <el-input v-model="emailDialog.form.subject" placeholder="请输入邮件主题" />
        </el-form-item>
        <el-form-item label="邮件正文" prop="text">
          <editor v-model="emailDialog.form.text" :min-height="192" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="sendEmail" :loading="emailDialog.loading">发 送</el-button>
        <el-button @click="emailDialog.open = false">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 发送短信对话框 -->
    <el-dialog title="发送短信" :visible.sync="messageDialog.open" width="650px" append-to-body>
      <el-form ref="emailForm" :model="messageDialog.form" label-width="80px">
        <el-form-item label="电话号码" prop="to">
          <el-input v-model="messageDialog.form.to" placeholder="请输入电话号码" :disabled="true" />
        </el-form-item>
        <el-form-item label="短信内容" prop="text">
          <editor v-model="messageDialog.form.text" :min-height="192" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="sendMessage" :loading="messageDialog.loading">发 送</el-button>
        <el-button @click="messageDialog.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listUser,
  getUser,
  delUser,
  addUser,
  updateUser,
  resetUserPwd,
  changeUserStatus,
  deptTreeSelect,
  sendSimpleEmail,
  apiDownloadTemplate,
} from "@/api/system/user";
import FileUpload from "@/components/FileUpload/index.vue";
import { getToken } from "@/utils/auth";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { Splitpanes, Pane } from "splitpanes";
import "splitpanes/dist/splitpanes.css";
import Editor from "@/components/Editor";
import { nationOptions } from "@/enums/commonEnum.js";

export default {
  name: "User",
  dicts: ["sys_normal_disable", "sys_user_sex", "grade"],
  components: { Treeselect, Splitpanes, Pane, Editor },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 弹出层标题
      title: "",
      // 所有组织树选项
      deptOptions: undefined,
      // 过滤掉已禁用组织树选项
      enabledDeptOptions: undefined,
      // 是否显示弹出层
      open: false,
      // 组织名称
      deptName: undefined,
      // 默认密码
      initPassword: undefined,
      // 日期范围
      dateRange: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label",
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData",
      },
      // 邮件发送参数
      emailDialog: {
        open: false,
        loading: false,
        form: {
          to: "",
          subject: "系统通知",
          text: "<p>尊敬的用户：</p><p>您好！</p><p>这是一条来自系统的通知邮件。</p><p>感谢您的使用。</p>",
        },
      },
      // 邮件发送参数
      messageDialog: {
        open: false,
        loading: false,
        form: {
          to: "",
          text: "尊敬的老师：请您尽快在系统中填写评审意见,点击提交评审成绩。",
        },
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
        phonenumber: undefined,
        status: undefined,
        deptId: undefined,
      },
      // 列信息
      columns: [
        { key: 0, label: `用户编号`, visible: true },
        { key: 1, label: `用户名称`, visible: true },
        { key: 2, label: `用户昵称`, visible: true },
        { key: 3, label: `组织`, visible: true },
        { key: 4, label: `手机号码`, visible: true },
        { key: 5, label: `状态`, visible: true },
        { key: 6, label: `创建时间`, visible: true },
      ],
      // 表单校验
      rules: {
        userName: [
          { required: true, message: "用户名称不能为空", trigger: "blur" },
          {
            min: 2,
            max: 20,
            message: "用户名称长度必须介于 2 和 20 之间",
            trigger: "blur",
          },
        ],
        nickName: [
          { required: true, message: "用户昵称不能为空", trigger: "blur" },
        ],
        password: [
          { required: true, message: "用户密码不能为空", trigger: "blur" },
          {
            min: 5,
            max: 20,
            message: "用户密码长度必须介于 5 和 20 之间",
            trigger: "blur",
          },
          {
            pattern: /^[^<>"'|\\]+$/,
            message: "不能包含非法字符：< > \" ' \\ |",
            trigger: "blur",
          },
        ],
        email: [
          {
            type: "email",
            message: "请输入正确的邮箱地址",
            pattern:
              /^([a-zA-Z0-9])+(([a-zA-Z0-9])|([._-][a-zA-Z0-9])*)+@([a-zA-Z0-9-])+((\.[a-zA-Z0-9-]{2,3}){1,2})$/,
            trigger: ["blur", "change"],
          },
        ],
        schoolPostalCode: [
          {
            type: "email",
            message: "请输入正确的邮箱地址",
            pattern:
              /^([a-zA-Z0-9])+(([a-zA-Z0-9])|([._-][a-zA-Z0-9])*)+@([a-zA-Z0-9-])+((\.[a-zA-Z0-9-]{2,3}){1,2})$/,
            trigger: ["blur", "change"],
          },
        ],
        phonenumber: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
        guardianPhone: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
        IDNumber: [
          {
            required: true,
            pattern:
              /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|))/,
            message: "请输入正确的身份证号码",
            trigger: "blur",
          },
        ],
        school: [
          { required: true, message: "请输入学校名称", trigger: "blur" },
        ],
        grade: [
          {
            required: true,
            message: "请选择年级",
            trigger: ["blur", "change"],
          },
        ],
        sex: [{ required: true, message: "请选择性别", trigger: "change" }],
      },
      nationOptions: nationOptions,
    };
  },
  watch: {
    // 根据名称筛选组织树
    deptName(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() {
    this.getList();
    this.getDeptTree();
    this.getConfigKey("sys.user.initPassword").then((response) => {
      this.initPassword = response.msg;
    });
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(
        (response) => {
          this.userList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    /** 查询组织下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then((response) => {
        this.deptOptions = response.data;
        this.enabledDeptOptions = this.filterDisabledDept(
          JSON.parse(JSON.stringify(response.data))
        );
      });
    },
    // 过滤禁用的组织
    filterDisabledDept(deptList) {
      return deptList.filter((dept) => {
        if (dept.disabled) {
          return false;
        }
        if (dept.children && dept.children.length) {
          dept.children = this.filterDisabledDept(dept.children);
        }
        return true;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id;
      this.handleQuery();
    },
    // 用户状态修改
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用";
      this.$modal
        .confirm('确认要"' + text + '""' + row.userName + '"用户吗？')
        .then(function () {
          return changeUserStatus(row.userId, row.status);
        })
        .then(() => {
          this.$modal.msgSuccess(text + "成功");
        })
        .catch(function () {
          row.status = row.status === "0" ? "1" : "0";
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        userId: undefined,
        deptId: 100,
        userName: undefined,
        nickName: undefined,
        password: undefined,
        phonenumber: undefined,
        sex: undefined,
        status: "0",
        postIds: [3],
        roleIds: [],
        email: undefined,
        dateOfBirth: undefined,
        IDNumber: undefined,
        schoolPostalCode: undefined,
        schoolPhone: undefined,
        school: undefined,
        schoolAddress: undefined,
        schoolManager: undefined,
        mailingAddress: undefined,
        mailingPostalCode: undefined,
        grade: undefined,
        nationality: undefined,
        fileUrl: undefined,
        schoolManager: undefined,
        guardian: undefined,
        guardianPhone: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.deptId = undefined;
      this.$refs.tree.setCurrentKey(null);
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.userId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    handleChangeFile(value, props) {
      this.form[props] = value;
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleResetPwd":
          this.handleResetPwd(row);
          break;
        case "handleAuthRole":
          this.handleAuthRole(row);
          break;
        default:
          break;
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      getUser().then((response) => {
        this.postOptions = response.posts;
        this.roleOptions = response.roles;
        this.open = true;
        this.title = "添加用户";
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const userId = row.userId || this.ids;
      getUser(userId).then((response) => {
        this.form = response.data;
        this.postOptions = response.posts;
        this.roleOptions = response.roles;
        this.$set(this.form, "postIds", response.postIds);
        this.$set(this.form, "roleIds", response.roleIds);
        this.open = true;
        this.title = "修改用户";
        this.form.password = "";
      });
    },
    /** 重置密码按钮操作 */
    handleResetPwd(row) {
      this.$prompt('请输入"' + row.userName + '"的新密码', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        inputPattern: /^.{5,20}$/,
        inputErrorMessage: "用户密码长度必须介于 5 和 20 之间",
        inputValidator: (value) => {
          if (/<|>|"|'|\||\\/.test(value)) {
            return "不能包含非法字符：< > \" ' \\ |";
          }
        },
      })
        .then(({ value }) => {
          resetUserPwd(row.userId, value).then((response) => {
            this.$modal.msgSuccess("修改成功，新密码是：" + value);
          });
        })
        .catch(() => { });
    },
    /** 分配角色操作 */
    handleAuthRole: function (row) {
      const userId = row.userId;
      this.$router.push("/system/user-auth/role/" + userId);
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // 准备提交数据
          const submitData = { ...this.form };

          // 处理出生年月日期字段
          if (submitData.dateOfBirth instanceof Date) {
            // 使用本地时间避免时区问题
            const year = submitData.dateOfBirth.getFullYear();
            const month = submitData.dateOfBirth.getMonth() + 1;
            submitData.dateOfBirth = `${year}-${String(month).padStart(2, '0')}`;
          }
          // 如果dateOfBirth已经是字符串格式，确保格式正确
          else if (typeof submitData.dateOfBirth === 'string' && submitData.dateOfBirth) {
            // 如果格式不是YYYY-MM，尝试转换
            if (!/^\d{4}-\d{2}$/.test(submitData.dateOfBirth)) {
              const date = new Date(submitData.dateOfBirth);
              if (!isNaN(date.getTime())) {
                const year = date.getFullYear();
                const month = date.getMonth() + 1;
                submitData.dateOfBirth = `${year}-${String(month).padStart(2, '0')}`;
              }
            }
          }

          if (this.form.userId != undefined) {
            updateUser(submitData).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addUser(submitData).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const userIds = row.userId || this.ids;
      this.$modal
        .confirm('是否确认删除用户编号为"' + userIds + '"的数据项？')
        .then(function () {
          return delUser(userIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/user/export",
        {
          ...this.queryParams,
        },
        `user_${new Date().getTime()}.xlsx`
      );
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download(
        "system/user/importTemplate",
        {},
        `user_template_${new Date().getTime()}.xlsx`
      );
    },
    // 下载同意书
    async importTemplateFile() {
      const { code, data } = await apiDownloadTemplate();
      if (code == 200 && data) {
        const cleanedUrl = new URL(data, window.location.href).href;
        const a = document.createElement("a");
        a.href = cleanedUrl;
        a.target = "_blank";
        a.download = "活动同意书模版";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      }
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(
        "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
        response.msg +
        "</div>",
        "导入结果",
        { dangerouslyUseHTMLString: true }
      );
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    // 打开发送邮件对话框
    handleSendEmail(row) {
      this.emailDialog.form.to = row.email;
      this.emailDialog.form.subject = "系统通知";
      this.emailDialog.form.text =
        "<p>尊敬的用户：</p><p>您好！</p><p>这是一条来自系统的通知邮件。</p><p>感谢您的使用。</p>";
      this.emailDialog.open = true;
    },
    // 发送邮件
    sendEmail() {
      this.emailDialog.loading = true;
      sendSimpleEmail(
        this.emailDialog.form.to,
        this.emailDialog.form.subject,
        this.emailDialog.form.text
      )
        .then((response) => {
          this.$modal.msgSuccess("邮件发送成功!");
          this.emailDialog.open = false;
        })
        .catch((error) => {
          this.$modal.msgError(
            "邮件发送失败：" + (error.message || "未知错误")
          );
        })
        .finally(() => {
          this.emailDialog.loading = false;
        });
    },
    // 打开发送短信对话框
    handleSendMessage(row) {
      this.messageDialog.form.to = row.phonenumber;
      this.messageDialog.form.text = "";
      this.messageDialog.open = true;
    },
    // 发送短信
    sendMessage() {
      this.messageDialog.loading = true;
      sendSimpleMessage(
        this.messageDialog.form.to,
        this.messageDialog.form.text
      )
        .then((response) => {
          this.$modal.msgSuccess("短信发送成功!");
          this.messageDialog.open = false;
        })
        .catch((error) => {
          this.$modal.msgError(
            "短信发送失败：" + (error.message || "未知错误")
          );
        })
        .finally(() => {
          this.messageDialog.loading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-select,
.el-date-editor {
  width: 100% !important;
}
</style>
