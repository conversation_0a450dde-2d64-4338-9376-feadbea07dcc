<template>
  <div>
    <div class="user-info-head" @click="editCropper" :class="{ 'disabled': disabled }">
      <img v-bind:src="currentImg" :title="disabled ? '编辑模式下可上传照片' : '点击上传照片'" class="img-lg" />
      <div v-if="isDefaultAvatar && !disabled" class="avatar-required-tip">
        <i class="el-icon-warning-outline"></i> 请上传头像
      </div>
    </div>
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body @opened="modalOpened"  @close="closeDialog">
      <el-row>
        <el-col :xs="24" :md="12" :style="{height: '450px'}">
          <vue-cropper
            ref="cropper"
            :img="options.img"
            :info="true"
            :autoCrop="options.autoCrop"
            :autoCropWidth="options.autoCropWidth"
            :autoCropHeight="options.autoCropHeight"
            :fixedBox="options.fixedBox"
            :outputType="options.outputType"
            @realTime="realTime"
            :crossOrigin="'anonymous'"
            v-if="visible"
          />
        </el-col>
        <el-col :xs="24" :md="12" :style="{height: '450px'}">
          <div class="avatar-upload-preview">
            <div class="preview-container" :style="previews.div">
              <img :src="previews.url" :style="previews.img" />
            </div>
          </div>
        </el-col>
      </el-row>
      <br />
      <el-row>
        <el-col :lg="2" :sm="3" :xs="3">
          <el-upload action="#" :http-request="requestUpload" :show-file-list="false" :before-upload="beforeUpload">
            <el-button size="small">
              选择
              <i class="el-icon-upload el-icon--right"></i>
            </el-button>
          </el-upload>
        </el-col>
        <el-col :lg="{span: 1, offset: 2}" :sm="2" :xs="2">
          <el-button icon="el-icon-plus" size="small" @click="changeScale(1)"></el-button>
        </el-col>
        <el-col :lg="{span: 1, offset: 1}" :sm="2" :xs="2">
          <el-button icon="el-icon-minus" size="small" @click="changeScale(-1)"></el-button>
        </el-col>
        <el-col :lg="{span: 1, offset: 1}" :sm="2" :xs="2">
          <el-button icon="el-icon-refresh-left" size="small" @click="rotateLeft()"></el-button>
        </el-col>
        <el-col :lg="{span: 1, offset: 1}" :sm="2" :xs="2">
          <el-button icon="el-icon-refresh-right" size="small" @click="rotateRight()"></el-button>
        </el-col>
        <el-col :lg="{span: 2, offset: 6}" :sm="2" :xs="2">
          <el-button type="primary" size="small" @click="uploadImg()">提 交</el-button>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import store from "@/store"
import { VueCropper } from "vue-cropper"
import { uploadAvatar, updateUserProfile } from "@/api/system/user"
import { debounce } from '@/utils'

export default {
  components: { VueCropper },
  props: {
    value: {
      type: String,
      default: ''
    },
    isTeamMember: {
      type: Boolean,
      default: false
    },
    memberId: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    currentImg() {
      console.log('Computing currentImg - value:', this.value, 'store avatar:', store.getters.avatar, 'isTeamMember:', this.isTeamMember);

      // 如果是团队成员且没有设置照片值，则显示默认照片
      if (this.isTeamMember) {
        const result = this.value || require('@/assets/images/profile.jpg');
        console.log('Team member currentImg result:', result);
        return result;
      }

      // 个人照片优先使用 value，然后是 store 中的照片
      let result;
      if (this.value) {
        result = this.value;
      } else if (store.getters.avatar) {
        result = store.getters.avatar;
      } else {
        result = require('@/assets/images/profile.jpg');
      }

      console.log('Personal currentImg result:', result);
      return result;
    },
    isDefaultAvatar() {
      // 检查是否使用默认头像
      const defaultAvatar = require('@/assets/images/profile.jpg');
      return this.currentImg === defaultAvatar || !this.value;
    }
  },
  data() {
    return {
      // 是否显示弹出层
      open: false,
      // 是否显示cropper
      visible: false,
      // 弹出层标题
      title: "修改照片",
      options: {
        img: '',  //裁剪图片的地址
        autoCrop: true,             // 是否默认生成截图框
        autoCropWidth: 295,         // 默认生成截图框宽度
        autoCropHeight: 413,        // 默认生成截图框高度
        fixedBox: true,             // 固定截图框大小 不允许改变
        outputType:"png",           // 默认生成截图为PNG格式
        filename: 'avatar'          // 文件名称
      },
      previews: {},
      resizeHandler: null
    }
  },
  created() {
    // 初始化裁剪图片地址
    this.options.img = this.currentImg;
  },
  watch: {
    value(newVal) {
      console.log('Value changed to:', newVal);
      if (newVal) {
        this.options.img = newVal;
      } else {
        // 如果值为空，使用当前图片
        this.options.img = this.currentImg;
      }
    },
    currentImg(newVal) {
      console.log('CurrentImg changed to:', newVal);
      // 当当前图片变化时，也更新裁剪器的图片
      if (newVal && !this.open) {
        this.options.img = newVal;
      }
    }
  },
  methods: {
    // 编辑照片
    editCropper() {
      if (this.disabled) {
        return; // 如果禁用状态，不响应点击
      }

      // 确保使用当前显示的头像图片
      const currentImage = this.currentImg;
      console.log('=== Opening cropper ===');
      console.log('Current image:', currentImage);
      console.log('this.value:', this.value);
      console.log('Store avatar:', store.getters.avatar);
      console.log('isTeamMember:', this.isTeamMember);
      console.log('Default avatar:', require('@/assets/images/profile.jpg'));

      // 直接设置图片，让vue-cropper处理加载
      this.options.img = currentImage;

      // 打开对话框
      this.open = true;
    },


    // 打开弹出层结束时的回调
    modalOpened() {
      this.visible = true
      // 重置预览数据
      this.previews = {}

      console.log('Modal opened with image:', this.options.img);

      if (!this.resizeHandler) {
        this.resizeHandler = debounce(() => {
          this.refresh()
        }, 100)
      }
      window.addEventListener("resize", this.resizeHandler)

      // 延迟刷新，确保组件完全渲染
      this.$nextTick(() => {
        setTimeout(() => {
          if (this.$refs.cropper) {
            this.refresh()
          }
        }, 300) // 增加延迟时间确保图片加载
      })
    },
    // 刷新组件
    refresh() {
      if (this.$refs.cropper && this.$refs.cropper.refresh) {
        this.$refs.cropper.refresh()
      }
    },
    // 覆盖默认的上传行为
    requestUpload() {
    },
    // 向左旋转
    rotateLeft() {
      this.$refs.cropper.rotateLeft()
    },
    // 向右旋转
    rotateRight() {
      this.$refs.cropper.rotateRight()
    },
    // 图片缩放
    changeScale(num) {
      num = num || 1
      this.$refs.cropper.changeScale(num)
    },
    // 上传预处理
    beforeUpload(file) {
      if (file.type.indexOf("image/") == -1) {
        this.$modal.msgError("文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。")
      } else {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = () => {
          this.options.img = reader.result
          this.options.filename = file.name
        }
      }
    },
    // 上传图片
    uploadImg() {
      this.$refs.cropper.getCropBlob(data => {
        let formData = new FormData()
        formData.append("avatarfile", data, this.options.filename)

        // 如果是团队成员，添加成员ID
        if (this.isTeamMember && this.memberId) {
          formData.append("memberId", this.memberId)
        }

        uploadAvatar(formData).then(response => {
          this.open = false
          // const imgUrl = process.env.VUE_APP_BASE_API + response.imgUrl;
          const imgUrl = response.imgUrl;

          // 如果是个人照片，则更新store
          if (!this.isTeamMember) {
            store.commit('SET_AVATAR', imgUrl)

            // 如果不是编辑模式，需要额外保存到后端并提示成功
            if (this.$parent && this.$parent.form && this.$parent.isEdit === false) {
              const form = {...this.$parent.form, avatar: imgUrl};
              updateUserProfile(form).then(() => {
                this.$modal.msgSuccess("修改成功");
              }).catch(err => {
                this.$modal.msgError("照片保存失败，请在编辑模式下重新上传");
                console.error("照片保存失败:", err);
              });
            } else {
              // 编辑模式下只提示上传成功，不提示修改成功
              this.$modal.msgSuccess("上传成功，请点击保存按钮保存修改");
            }
          } else {
            // 团队成员照片上传成功
            this.$modal.msgSuccess("上传成功，请点击保存按钮保存修改");
          }

          // 通过v-model更新父组件的值
          this.$emit('input', imgUrl)

          this.visible = false
        })
      })
    },
    // 实时预览
    realTime(data) {
      this.previews = data;
      // 确保预览数据正确更新
      this.$nextTick(() => {
        if (this.previews && this.previews.url) {
          console.log('Preview data updated:', this.previews);
        }
      });
    },
    // 关闭窗口
    closeDialog() {
      this.visible = false
      window.removeEventListener("resize", this.resizeHandler)
    },
    // 添加验证方法，供父组件调用
    validateAvatar() {
      if (this.isDefaultAvatar) {
        this.$modal.msgError("请上传头像");
        return false;
      }
      return true;
    }
  }
}
</script>
<style scoped lang="scss">
.user-info-head {
  position: relative;
  display: inline-block;
  height: 120px;
  width: 100px;
}

.user-info-head:hover:after {
  content: '+';
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  color: #eee;
  background: rgba(0, 0, 0, 0.5);
  font-size: 24px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  cursor: pointer;
  line-height: 110px;
  text-align: center;
  /* 移除 border-radius: 50%; */
}

/* 禁用状态下不显示加号和鼠标手型 */
.user-info-head.disabled {
  cursor: default;
}

.user-info-head.disabled:hover:after {
  display: none;
}

/* 添加新的图片样式 */
.img-lg {
  width: 100px;
  height: 140px;
  object-fit: cover; /* 保持图片比例并填充容器 */
  border: 1px solid #ddd; /* 添加边框 */
}

/* 添加必填提示样式 */
.avatar-required-tip {
  position: absolute;
  bottom: -20px;
  left: 0;
  width: 100%;
  text-align: center;
  color: #F56C6C;
  font-size: 12px;
}

/* 添加预览区域样式 */
.avatar-upload-preview {
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 295px;
  height: 413px;
  border: 1px solid #ccc;
  background: #f5f5f5;
  overflow: hidden;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .preview-container {
    width: 100%;
    height: 100%;
    overflow: hidden;

    img {
      max-width: none;
      max-height: none;
    }
  }
}
</style>
