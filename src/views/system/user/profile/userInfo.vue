<template>
  <el-form ref="form" :model="form" :rules="rules" label-width="80px" label-position="top">
    <el-row :gutter="32">
      <el-col :span="12">
        <el-form-item label="姓名" prop="nickName">
          <el-input v-model="form.nickName" maxlength="30" />
        </el-form-item>
        <el-form-item label="身份证号" prop="IDNumber">
          <el-input v-model="form.IDNumber" placeholder="请输入身份证号" />
        </el-form-item>
        <el-form-item label="学校名称" prop="school">
          <el-input v-model="form.school" placeholder="请输入学校名称" maxlength="50" />
        </el-form-item>
        <el-form-item label="用户性别">
          <el-select v-model="form.sex" placeholder="请选择性别">
            <el-option v-for="dict in dict.type.sys_user_sex" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="学校邮箱" prop="schoolPostalCode">
          <el-input v-model="form.schoolPostalCode" placeholder="请输入学校邮箱" maxlength="50" />
        </el-form-item>

        <el-form-item label="邮件地址" prop="mailingAddress">
          <el-input v-model="form.mailingAddress" placeholder="请输入邮件地址" />
        </el-form-item>
        <el-form-item label="邮件地址邮政编码" prop="mailingPostalCode">
          <el-input v-model="form.mailingPostalCode" placeholder="请输入邮件地址邮政编码" />
        </el-form-item>
        <el-form-item label="学校负责人" prop="schoolManager">
          <el-input v-model="form.schoolManager" placeholder="请输入学校负责人" maxlength="10" />
        </el-form-item>
        <el-form-item label="监护人" prop="guardian">
          <el-input v-model="form.guardian" placeholder="请输入监护人" maxlength="10" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="手机号码" prop="phonenumber">
          <el-input v-model="form.phonenumber" maxlength="11" />
        </el-form-item>
        <el-form-item label="年级" prop="grade">
          <el-select v-model="form.grade" placeholder="请选择年级">
            <el-option v-for="dict in dict.type.grade" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="民族" prop="nationality">
          <el-select v-model="form.nationality" placeholder="请选择民族">
            <el-option v-for="item in nationOptions" :key="item.label" :label="item.label"
              :value="item.label"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" maxlength="50" />
        </el-form-item>
        <el-form-item label="出生年月" prop="dateOfBirth">
          <el-date-picker
            v-model="form.dateOfBirth"
            type="month"
            placeholder="选择日期"
            value-format="yyyy-MM"
            format="yyyy年MM月">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="学校地址" prop="schoolAddress">
          <el-input v-model="form.schoolAddress" placeholder="请输入学校地址" />
        </el-form-item>
        <el-form-item label="学校电话" prop="schoolPhone">
          <el-input v-model="form.schoolPhone" placeholder="请输入学校电话" maxlength="11" />
        </el-form-item>
        <el-form-item label="监护人电话" prop="guardianPhone">
          <el-input v-model="form.guardianPhone" placeholder="请输入手机号码" maxlength="11" />
        </el-form-item>
        <el-form-item label="活动同意书" prop="fileUrl">
          <FileUpload :fileSize="20" :limit="1" :value="form.fileUrl" :isShowTip="false" :fileType="['doc', 'docx']"
            @input="handleChangeFile($event, 'fileUrl')" />
          <el-button type="text" :disabled="false" @click="importTemplateFile">下载模版</el-button>
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item>
      <el-button type="primary" size="mini" @click="submit">保存</el-button>
      <el-button type="danger" size="mini" @click="close">关闭</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { updateUserProfile, apiDownloadTemplate } from "@/api/system/user";
import { nationOptions } from "@/enums/commonEnum.js";
import FileUpload from "@/components/FileUpload/index.vue";
export default {
  dicts: ["sys_normal_disable", "sys_user_sex", "grade"],
  props: {
    user: {
      type: Object,
    },
  },
  data() {
    return {
      form: {},
      // 表单校验
      rules: {
        userName: [
          { required: true, message: "用户名称不能为空", trigger: "blur" },
          {
            min: 2,
            max: 20,
            message: "用户名称长度必须介于 2 和 20 之间",
            trigger: "blur",
          },
        ],
        nickName: [
          { required: true, message: "用户昵称不能为空", trigger: "blur" },
        ],
        email: [
          {
            type: "email",
            message: "请输入正确的邮箱地址",
            pattern:
              /^([a-zA-Z0-9])+(([a-zA-Z0-9])|([._-][a-zA-Z0-9])*)+@([a-zA-Z0-9-])+((\.[a-zA-Z0-9-]{2,3}){1,2})$/,
            trigger: ["blur", "change"],
          },
        ],
        schoolPostalCode: [
          {
            type: "email",
            message: "请输入正确的邮箱地址",
            pattern:
              /^([a-zA-Z0-9])+(([a-zA-Z0-9])|([._-][a-zA-Z0-9])*)+@([a-zA-Z0-9-])+((\.[a-zA-Z0-9-]{2,3}){1,2})$/,
            trigger: ["blur", "change"],
          },
        ],
        phonenumber: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
        guardianPhone: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
        IDNumber: [
          {
            required: true,
            pattern:
              /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|))/,
            message: "请输入正确的身份证号码",
            trigger: "blur",
          },
        ],
        school: [
          { required: true, message: "请输入学校名称", trigger: "blur" },
        ],
        grade: [
          {
            required: true,
            message: "请选择年级",
            trigger: ["blur", "change"],
          },
        ],
        sex: [{ required: true, message: "请选择性别", trigger: "change" }],
      },
      nationOptions: nationOptions,
    };
  },
  watch: {
    user: {
      handler(user) {
        if (user) {
          this.form = {
            ...user,
          };
        }
      },
      immediate: true,
    },
  },
  methods: {
    submit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // 准备提交数据
          const submitData = { ...this.form };

          // 处理出生年月日期字段
          if (submitData.dateOfBirth instanceof Date) {
            // 使用本地时间避免时区问题
            const year = submitData.dateOfBirth.getFullYear();
            const month = submitData.dateOfBirth.getMonth() + 1;
            submitData.dateOfBirth = `${year}-${String(month).padStart(2, '0')}`;
          }
          // 如果dateOfBirth已经是字符串格式，确保格式正确
          else if (typeof submitData.dateOfBirth === 'string' && submitData.dateOfBirth) {
            // 如果格式不是YYYY-MM，尝试转换
            if (!/^\d{4}-\d{2}$/.test(submitData.dateOfBirth)) {
              const date = new Date(submitData.dateOfBirth);
              if (!isNaN(date.getTime())) {
                const year = date.getFullYear();
                const month = date.getMonth() + 1;
                submitData.dateOfBirth = `${year}-${String(month).padStart(2, '0')}`;
              }
            }
          }

          updateUserProfile(submitData).then((response) => {
            this.$modal.msgSuccess("修改成功");

            this.$emit("updateUser", submitData);
          });
        }
      });
    },
    // 下载同意书
    async importTemplateFile() {
      const { code, data } = await apiDownloadTemplate();
      if (code == 200 && data) {
        const cleanedUrl = new URL(data, window.location.href).href;
        const a = document.createElement("a");
        a.href = cleanedUrl;
        a.target = "_blank";
        a.download = "活动同意书模版";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      }
    },

    close() {
      this.$tab.closePage();
    },
    handleChangeFile(value, props) {
      this.form[props] = value;
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-select,
.el-date-editor {
  width: 100% !important;
}
</style>
