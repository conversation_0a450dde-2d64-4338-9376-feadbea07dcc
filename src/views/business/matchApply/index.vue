<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="申请编号" prop="applyNo">
        <el-input
          v-model="queryParams.applyNo"
          placeholder="请输入申请编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属组织" prop="deptId">
        <el-select
          v-model="queryParams.deptId"
          placeholder="请选择所属组织"
          clearable
        >
          <el-option
            v-for="dict in dict.type.beijing_area"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="选手类型" prop="playerType">
        <el-select
          v-model="queryParams.playerType"
          placeholder="请选择选手类型"
          clearable
        >
          <el-option
            v-for="dict in dict.type.attend_group"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="活动赛项" prop="joinDoings">
        <el-select
          v-model="queryParams.joinDoings"
          placeholder="请选择活动赛项"
          clearable
        >
          <el-option
            v-for="dict in dict.type.doings"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus">
        <el-select
          v-model="queryParams.auditStatus"
          placeholder="请选择审核状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.audit_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!--      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['business:matchApply:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['business:matchApply:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['business:matchApply:remove']">删除</el-button>
      </el-col>
      -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['business:matchApply:export']"
          >导出申报数据</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="matchApplyList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        label="申请编号"
        width="180"
        align="center"
        prop="applyNo"
      />
      <el-table-column label="归属地" align="center" prop="deptId">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.beijing_area"
            :value="scope.row.deptId"
          />
        </template>
      </el-table-column>
      <el-table-column label="选手类型" align="center" prop="playerType">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.attend_group"
            :value="scope.row.playerType"
          />
        </template>
      </el-table-column>
      <el-table-column label="组织名称" align="center" prop="orgName" />
      <el-table-column label="项目名称" align="center" prop="projectName" />
      <el-table-column label="活动赛项" align="center" prop="joinDoings">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.doings" :value="scope.row.joinDoings" />
        </template>
      </el-table-column>
      <el-table-column label="申报时间" align="center" prop="createTime" />
      <el-table-column label="审核状态" align="center" prop="auditStatus">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.audit_status"
            :value="scope.row.auditStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <!--
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['business:matchApply:edit']">修改</el-button>
-->
          <!--
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['business:matchApply:remove']">删除</el-button>
-->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="openDetailForm(scope.row)"
            >查看详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改比赛申报对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="申请编号" prop="applyNo">
          <el-input v-model="form.applyNo" placeholder="请输入申请编号" />
        </el-form-item>
        <el-form-item label="所属用户" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入所属用户" />
        </el-form-item>
        <el-form-item label="所属组织" prop="deptId">
          <el-select v-model="form.deptId" placeholder="请选择所属组织">
            <el-option
              v-for="dict in dict.type.beijing_area"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选手类型" prop="playerType">
          <el-select v-model="form.playerType" placeholder="请选择选手类型">
            <el-option
              v-for="dict in dict.type.attend_group"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="组织名称" prop="orgName">
          <el-input v-model="form.orgName" placeholder="请输入组织名称" />
        </el-form-item>
        <el-form-item label="下级组织名称" prop="orgSubsetName">
          <el-input
            v-model="form.orgSubsetName"
            placeholder="请输入下级组织名称"
          />
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="活动赛项" prop="joinDoings">
          <el-select v-model="form.joinDoings" placeholder="请选择活动赛项">
            <el-option
              v-for="dict in dict.type.doings"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属市" prop="belongCity">
          <el-input v-model="form.belongCity" placeholder="请输入所属市" />
        </el-form-item>
        <el-form-item label="所属区" prop="belongArea">
          <el-input v-model="form.belongArea" placeholder="请输入所属区" />
        </el-form-item>
        <el-form-item label="队长姓名" prop="teamLeaderName">
          <el-input
            v-model="form.teamLeaderName"
            placeholder="请输入队长姓名"
          />
        </el-form-item>
        <el-form-item label="队长电话" prop="teamLeaderPhone">
          <el-input
            v-model="form.teamLeaderPhone"
            placeholder="请输入队长电话"
          />
        </el-form-item>
        <el-form-item label="队长邮箱" prop="teamLeaderEmail">
          <el-input
            v-model="form.teamLeaderEmail"
            placeholder="请输入队长邮箱"
          />
        </el-form-item>
        <el-form-item label="队长学历" prop="teamLeaderQual">
          <el-input
            v-model="form.teamLeaderQual"
            placeholder="请输入队长学历"
          />
        </el-form-item>
        <el-form-item label="指导老师" prop="instructorName">
          <el-input
            v-model="form.instructorName"
            placeholder="请输入指导老师"
          />
        </el-form-item>
        <el-form-item label="指导老师电话" prop="instructorPhone">
          <el-input
            v-model="form.instructorPhone"
            placeholder="请输入指导老师电话"
          />
        </el-form-item>
        <el-form-item label="队员信息" prop="memberInfo">
          <el-input v-model="form.memberInfo" placeholder="请输入队员信息" />
        </el-form-item>
        <el-form-item label="活动同意书" prop="letterAgreement">
          <el-input
            v-model="form.letterAgreement"
            placeholder="请输入活动同意书"
          />
        </el-form-item>
        <el-form-item label="活动作品" prop="achievement">
          <el-input v-model="form.achievement" placeholder="请输入活动作品" />
        </el-form-item>
        <el-form-item label="审核状态" prop="auditStatus">
          <el-select v-model="form.auditStatus" placeholder="请选择审核状态">
            <el-option
              v-for="dict in dict.type.audit_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="删除标记" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标记" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listMatchApply,
  getMatchApply,
  delMatchApply,
  addMatchApply,
  updateMatchApply,
} from "@/api/business/matchApply";

export default {
  name: "MatchApply",
  dicts: ["attend_group", "doings", "beijing_area", "audit_status"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 比赛申报表格数据
      matchApplyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        applyNo: null,
        deptId: null,
        playerType: null,
        projectName: null,
        joinDoings: null,
        auditStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        applyNo: [
          { required: true, message: "申请编号不能为空", trigger: "blur" },
        ],
        auditStatus: [
          { required: true, message: "审核状态不能为空", trigger: "change" },
        ],
        delFlag: [
          { required: true, message: "删除标记不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询比赛申报列表 */
    getList() {
      this.loading = true;
      listMatchApply(this.queryParams).then((response) => {
        this.matchApplyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        applyNo: null,
        userId: null,
        deptId: null,
        playerType: null,
        orgName: null,
        orgSubsetName: null,
        projectName: null,
        joinDoings: null,
        belongCity: null,
        belongArea: null,
        teamLeaderName: null,
        teamLeaderPhone: null,
        teamLeaderEmail: null,
        teamLeaderQual: null,
        instructorName: null,
        instructorPhone: null,
        memberInfo: null,
        letterAgreement: null,
        achievement: null,
        auditStatus: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
        remark: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加比赛申报";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getMatchApply(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改比赛申报";
      });
    },
    /** 查看详情 */
    openDetailForm(row) {
      this.reset();
      this.$router.push({
        path: "/matchApplyForm",
        query: {
          applyId: row.id,
          applyNo: row.applyNo,
        },
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateMatchApply(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMatchApply(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除比赛申报编号为"' + ids + '"的数据项？')
        .then(function () {
          return delMatchApply(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "business/matchApply/export",
        {
          ...this.queryParams,
        },
        `matchApply_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
