<template>
  <div class="app-container">
    <el-row>
      <h3 class="personal_title">申报信息审批进度详情</h3>
    </el-row>
    <!-- Tab -->
    <!--    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">-->
    <!--      <el-tab-pane label="审批进度详情" name="first" key="first" class="scrollable-tab-pane">-->
    <div class="timelineProcessBox">
      <el-timeline class="timeline">
        <el-timeline-item
          class="lineitem"
          placement="top"
          :class="[
            activity.done ? 'el-timeline-item-active' : 'inactive',
            !activity.auditStatus ? 'el-timeline-item-failed' : '',
          ]"
          v-for="(activity, index) in activities"
          :key="index"
          :hide-timestamp="true"
        >
          <el-tooltip
            slot="dot"
            class="item"
            effect="dark"
            :disabled="!activity.auditMark"
            :content="activity.auditMark"
            placement="top-start"
          >
            <div class="dot"></div>
          </el-tooltip>

          <span style="display: flex; flex-direction: column">
            <span style="margin: 5px 0; font-size: 15px; margin-left: 1rem">
              {{ activity.content }}
            </span>
            <span
              style="
                margin: 5px 0;
                font-size: 14px;
                margin-left: 1rem;
                color: #666;
              "
            >
              {{ activity.time }}
            </span>
          </span>
        </el-timeline-item>
      </el-timeline>
    </div>
    <!--      </el-tab-pane>-->
    <!--    </el-tabs>-->
    <el-row>
      <h3 class="personal_title">活动团队信息</h3>
    </el-row>
    <el-card style="padding-top: 10px">
      <el-form
        ref="applyForm"
        :model="applyForm"
        size="medium"
        label-width="120px"
        disabled
      >
        <el-row :gutter="100">
          <el-col :span="8">
            <el-form-item label="选手类型" prop="playerType">
              <el-select v-model="applyForm.playerType" clearable disabled>
                <el-option
                  v-for="dict in dict.type.attend_group"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="组织名称" prop="orgName">
              <el-input v-model="applyForm.orgName" clearable disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="下级组织名称" prop="orgSubsetName">
              <el-input v-model="applyForm.orgSubsetName" clearable disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="100">
          <el-col :span="8">
            <el-form-item label="队长姓名" prop="teamLeaderName">
              <el-input v-model="applyForm.teamLeaderName" clearable disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="队长电话" prop="teamLeaderPhone">
              <el-input
                v-model="applyForm.teamLeaderPhone"
                clearable
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="队长邮箱" prop="teamLeaderEmail">
              <el-input
                v-model="applyForm.teamLeaderEmail"
                clearable
                disabled
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="100">
          <el-col :span="8">
            <el-form-item label="队长学历" prop="idCardNo">
              <el-input v-model="applyForm.teamLeaderQual" clearable disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="指导老师" prop="instructorName">
              <el-input v-model="applyForm.instructorName" clearable disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="指导老师电话" prop="registerNoteNo">
              <el-input
                v-model="applyForm.instructorPhone"
                clearable
                disabled
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-row>
      <h3 class="personal_title">活动项目信息</h3>
    </el-row>
    <el-card style="padding-top: 10px">
      <el-form
        ref="applyForm"
        :model="applyForm"
        size="medium"
        label-width="120px"
        disabled
      >
        <el-row :gutter="100">
          <el-col :span="8">
            <el-form-item label="项目名称" prop="projectName">
              <el-input v-model="applyForm.projectName" clearable disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="活动赛项" prop="joinDoings">
              <el-select v-model="applyForm.joinDoings" clearable disabled>
                <el-option
                  v-for="dict in dict.type.doings"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属区" prop="belongArea">
              <el-input v-model="applyForm.belongArea" clearable disabled />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-row>
      <h3 class="personal_title">活动同意书以及作品</h3>
    </el-row>
    <el-card style="padding-top: 10px">
      <el-form
        ref="applyForm"
        :model="applyForm"
        size="medium"
        label-width="120px"
        disabled
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="活动同意书" prop="letterAgreement">
              <image-upload v-model="applyForm.letterAgreement" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="活动作品" prop="achievement">
              <image-upload v-model="applyForm.letterAgreement" disabled />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="button_warp">
        <el-button
          type="success"
          @click="areaAudit"
          v-hasPermi="['business:matchApply:areaAudit']"
          >区审核</el-button
        >
        <el-button
          type="success"
          @click="cityAudit"
          v-hasPermi="['business:matchApply:cityAudit']"
          >市审核</el-button
        >
      </div>
    </el-card>

    <!-- 区审核对话框 -->
    <el-dialog
      title="区审核"
      :visible.sync="areaAuditDialogVisible"
      width="500px"
      append-to-body
    >
      <el-form
        ref="areaAuditForm"
        :model="areaAuditForm"
        :rules="areaAuditRules"
        label-width="80px"
      >
        <el-form-item label="审核结果" prop="auditResult">
          <el-radio-group
            v-model="areaAuditForm.auditResult"
            @change="handleAuditResultChange"
          >
            <el-radio label="1">通过</el-radio>
            <el-radio label="0">不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审批意见" prop="auditMark">
          <el-input
            type="textarea"
            v-model="areaAuditForm.auditMark"
            placeholder="请输入审批意见"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAreaAudit">确 定</el-button>
        <el-button @click="areaAuditDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 市审核对话框 -->
    <el-dialog
      title="市审核"
      :visible.sync="cityAuditDialogVisible"
      width="500px"
      append-to-body
    >
      <el-form
        ref="cityAuditForm"
        :model="cityAuditForm"
        :rules="cityAuditRules"
        label-width="80px"
      >
        <el-form-item label="审核结果" prop="auditResult">
          <el-radio-group
            v-model="cityAuditForm.auditResult"
            @change="handleCityAuditResultChange"
          >
            <el-radio label="1">通过</el-radio>
            <el-radio label="0">不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审批意见" prop="auditMark">
          <el-input
            type="textarea"
            v-model="cityAuditForm.auditMark"
            placeholder="请输入审批意见"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitCityAudit">确 定</el-button>
        <el-button @click="cityAuditDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as api from "../../../../api/business/matchApply.js";
import {
  getMatchApply,
  getAuditNodeData,
  areaAudit,
  cityAudit,
} from "../../../../api/business/matchApply.js";
import { baseURL } from "@/utils/request";
import { getUserProfile } from "@/api/system/user";

export default {
  components: {},
  dicts: ["attend_group", "doings", "beijing_area", "audit_status"],
  data() {
    const timelineList = [];
    return {
      // 表单参数
      applyForm: {},
      options: [],
      activeName: "first", // Default to 'first' tab
      timelineList: timelineList,
      activities: [
        {
          content: "初始化",
          time: "",
          done: false,
          auditStatus: false,
          auditMark: "",
        },
      ],
      userInfo: {},
      deptInfo: {},
      areaAuditDialogVisible: false,
      areaAuditForm: {
        auditResult: "1",
        auditMark: "通过",
      },
      areaAuditRules: {
        auditResult: [
          { required: true, message: "请选择审核结果", trigger: "change" },
        ],
        auditMark: [
          { required: true, message: "请输入审批意见", trigger: "blur" },
        ],
      },
      cityAuditDialogVisible: false,
      cityAuditForm: {
        auditResult: "1",
        auditMark: "通过",
      },
      cityAuditRules: {
        auditResult: [
          { required: true, message: "请选择审核结果", trigger: "change" },
        ],
        auditMark: [
          { required: true, message: "请输入审批意见", trigger: "blur" },
        ],
      },
    };
  },
  computed: {
    letterAgreementUrl() {
      return this.applyForm.letterAgreement
        ? baseURL + this.applyForm.letterAgreement
        : "";
    },
    achievementUrl() {
      return this.applyForm.achievement
        ? baseURL + this.applyForm.achievement
        : "";
    },
  },
  created() {
    if (this.$route.query.applyId) {
      this.getMatchApplyInfo();
    }
    // 初始化加载数据
    this.loadInitialData();
    // 获取用户信息
    this.getUserInfo();
  },
  methods: {
    getUserInfo() {
      getUserProfile().then((response) => {
        this.userInfo = response.data;
        this.deptInfo = response.data.dept;
        console.log("当前用户部门信息：", this.deptInfo);
      });
    },
    async loadInitialData() {
      const { data: gmData } = await getAuditNodeData(
        this.$route.query.applyNo
      );
      this.auditNodeList = gmData;
      // 直接将接口返回的数据赋值给activities
      this.activities = gmData;
    },
    getMatchApplyInfo() {
      api.getMatchApply(this.$route.query.applyId).then((res) => {
        this.applyForm = res.data;
      });
    },

    //提交保存主要信息
    auditCommit() {},
    areaAudit() {
      this.areaAuditDialogVisible = true;
      this.areaAuditForm = {
        auditResult: "1",
        auditMark: "通过",
      };
    },
    handleAuditResultChange(value) {
      if (value === "1") {
        this.areaAuditForm.auditMark = "通过";
      } else {
        this.areaAuditForm.auditMark = "";
      }
    },
    submitAreaAudit() {
      this.$refs["areaAuditForm"].validate((valid) => {
        if (valid) {
          const params = {
            applyNo: this.$route.query.applyNo,
            ...this.areaAuditForm,
          };
          api.areaAudit(params).then((response) => {
            this.$modal.msgSuccess("审核成功");
            this.areaAuditDialogVisible = false;
            // 刷新数据
            // this.getMatchApplyInfo();
            this.loadInitialData();
          });
        }
      });
    },
    auditApplyNotPass() {},

    handleItemClick(activity) {
      console.log("Clicked activity:", activity);
      // Add any additional handling logic here
    },
    cityAudit() {
      this.cityAuditDialogVisible = true;
      this.cityAuditForm = {
        auditResult: "1",
        auditMark: "通过",
      };
    },
    handleCityAuditResultChange(value) {
      if (value === "1") {
        this.cityAuditForm.auditMark = "通过";
      } else {
        this.cityAuditForm.auditMark = "";
      }
    },
    submitCityAudit() {
      this.$refs["cityAuditForm"].validate((valid) => {
        if (valid) {
          const params = {
            applyNo: this.$route.query.applyNo,
            ...this.cityAuditForm,
          };
          api.cityAudit(params).then((response) => {
            this.$modal.msgSuccess("审核成功");
            this.cityAuditDialogVisible = false;
            // 刷新数据
            // this.getMatchApplyInfo();
            this.loadInitialData();
          });
        }
      });
    },
  },
};
</script>


<style lang="scss" scoped>
.app-container {
  min-width: 1200px;
}

.button_warp {
  display: flex;
  justify-content: right;
  margin-bottom: 20px;
}
.scrollable-tab-pane {
  height: 182px;
  /* 设置固定高度 */
  overflow-y: auto;
  /* 垂直滚动条 */
}

.timelineProcessBox {
  width: 90rem;
  height: 120px;
  border: 1px solid #60d6cd;
  // background: rgba($color: #3bcdc2, $alpha: 0.3);
  background: #eff8f8;
  border-radius: 6px;
  margin-bottom: 0px;
  padding: 20px;
  box-sizing: content-box;
  display: flex;
  align-items: center;

  .button1 {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #0e8ca1;
    font-weight: bold;
    font-size: 18px;
    border: 1px solid #666;
    width: 120px;
    height: 40px;
    border-radius: 5px;
  }

  .timeline {
    display: flex;
    flex: 1;
    margin: 40px auto;

    .lineitem {
      transform: translateX(50%);
      width: 22%;
    }
  }
}

//圆点样式
.dot {
  border-bottom: 4px solid #3bcdc2 !important;
  background: #fff;
  width: 11px;
  height: 11px;
  border-radius: 50%;
  border: 4px solid #3bcdc2;
}

// 流程线条样式
::v-deep .el-timeline-item__tail {
  border-left: none !important;
  border-top: 4px solid #e4e7ed !important;
  width: 100% !important;
  position: absolute !important;
  top: 0px;
}

::v-deep .el-timeline-item__wrapper {
  padding-left: 0;
  position: absolute !important;
  top: 20px !important;
  transform: translateX(-50%) !important;
  text-align: center !important;
}

::v-deep .el-timeline-item__timestamp {
  font-size: 14px;
}

::v-deep .el-timeline-item__dot {
  margin-top: -10px;
}

.el-timeline-item-active {
  ::v-deep .el-timeline-item__node {
    background-color: #06b6a9 !important;
  }

  ::v-deep .el-timeline-item__tail {
    border-color: #06b6a9 !important;
  }

  // &:last-child {
  //   ::v-deep .el-timeline-item__tail {
  //     border-color: #e4e7ed !important;
  //   }
  // }
}

//选项卡右侧
::v-deep .el-tabs--card > .el-tabs__header .el-tabs__nav {
  float: right;
}

// 有active样式的下一个li
.el-timeline-item-active + li {
  ::v-deep .el-timeline-item__node {
    background-color: "#000";
  }
}

.el-timeline-item-failed {
  ::v-deep .el-timeline-item__node {
    background-color: #f56c6c !important;
  }

  ::v-deep .el-timeline-item__tail {
    border-color: #f56c6c !important;
  }

  .dot {
    border-color: #f56c6c !important;
  }
}
</style>
<style lang="scss" scoped>
.el-dialog3 .el-dialog__header {
  /* display: none; */
  pointer-events: fill;
}

.el-dialog3 .el-dialog {
  height: 80% !important;
  overflow: auto;
}

.tubiao {
  overflow-y: auto;
  width: 1820px;
  height: 61rem;
  /* 设置一个固定的高度 */
  margin: auto;
}

.list-title {
  padding: 20px 0 20px 0;
}

.el-col1 {
  margin-left: 50px;
}

.left_div2 {
  height: 28rem;
}

.timeaxis {
  width: 800px;
  height: 200px;
  margin-top: 30px;
}

.box {
  float: left;
  width: 180px;
}

.circular {
  border: 2px solid #67c23a;
  width: 10px;
  height: 10px;
  border-radius: 10px;
  background: white;
  margin: auto;
  margin-bottom: -4px;
  position: relative;
  top: -8px;
  left: 85px;
}

.item {
  border-bottom: 1px solid #409eff;
  position: relative;

  .left {
    position: absolute;
    left: 55px;
    top: -25px;
  }

  .center {
    position: absolute;
    left: 165px;
    top: -26px;
  }
}

.item2 {
  position: relative;

  .bottom {
    position: absolute;
    left: 75px;
    top: 0px;
  }
}
.personal_title {
  border: 1px solid #cdcdcd;
  padding: 10px 20px;
  background-color: #cdcdcd;
}
</style>
