<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="活动年份" prop="doingsYear">
        <el-input
          v-model="queryParams.doingsYear"
          placeholder="请输入活动年份"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="活动编号" prop="doingsNo">
        <el-input
          v-model="queryParams.doingsNo"
          placeholder="请输入活动编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="活动名称" prop="doingsName">
        <el-input
          v-model="queryParams.doingsName"
          placeholder="请输入活动名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="主题" prop="subject">
        <el-input
          v-model="queryParams.subject"
          placeholder="请输入主题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="[' business:doings:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="[' business:doings:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="[' business:doings:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="[' business:doings:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="doingsList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        label="活动年份"
        width="100"
        align="center"
        prop="doingsYear"
      />
      <el-table-column
        label="活动编号"
        width="100"
        align="center"
        prop="doingsNo"
      />
      <el-table-column label="活动名称" align="center" prop="doingsName" />
      <el-table-column label="主题" align="center" prop="subject" />
      <!--      <el-table-column label="涉及领域" align="center" prop="coverRange" />-->
      <el-table-column label="活动目标" align="center" prop="purpoe" />
      <el-table-column label="活动群体" align="center" prop="attendGroup">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.attend_group"
            :value="
              scope.row.attendGroup ? scope.row.attendGroup.split(',') : []
            "
          />
        </template>
      </el-table-column>
      <el-table-column
        label="活动人数"
        width="150"
        align="center"
        prop="attendNum"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.attend_num"
            :value="scope.row.attendNum ? scope.row.attendNum.split(',') : []"
          />
        </template>
      </el-table-column>
      <el-table-column label="备注" width="100" align="center" prop="remark" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="[' business:doings:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="[' business:doings:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改比赛活动对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="活动名称" prop="matchId">
          <el-select v-model="form.matchId" placeholder="请选择活动">
            <el-option
              v-for="dict in dict.type.match"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="活动年份" prop="doingsYear">
          <el-select v-model="form.doingsYear" placeholder="请选择活动年份">
            <el-option key="6" label="2025" value="2025"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="活动编号" prop="doingsNo">
          <el-input v-model="form.doingsNo" placeholder="请输入活动编号" />
        </el-form-item>
        <el-form-item label="活动名称" prop="doingsName">
          <el-input v-model="form.doingsName" placeholder="请输入活动名称" />
        </el-form-item>
        <el-form-item label="年度主题" prop="subject">
          <el-input v-model="form.subject" placeholder="请输入主题" />
        </el-form-item>
        <!--        <el-form-item label="涉及领域" prop="coverRange">-->
        <!--          <el-input v-model="form.coverRange" type="textarea" placeholder="请输入内容" />-->
        <!--        </el-form-item>-->
        <el-form-item label="活动目标" prop="purpoe">
          <el-input
            v-model="form.purpoe"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="活动群体" prop="attendGroup">
          <el-checkbox-group v-model="form.attendGroup">
            <el-checkbox
              v-for="dict in dict.type.attend_group"
              :key="dict.value"
              :label="dict.value"
            >
              {{ dict.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="活动人数" prop="attendNum">
          <el-checkbox-group v-model="form.attendNum">
            <el-checkbox
              v-for="dict in dict.type.attend_num"
              :key="dict.value"
              :label="dict.value"
            >
              {{ dict.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listDoings,
  getDoings,
  delDoings,
  addDoings,
  updateDoings,
} from "@/api/business/doings";

export default {
  name: "Doings",
  dicts: ["attend_group", "attend_num", "match"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 比赛活动表格数据
      doingsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        doingsYear: null,
        doingsNo: null,
        doingsName: null,
        subject: null,
        attendGroup: null,
        attendNum: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        matchId: [{ required: true, message: "请选择活动", trigger: "blur" }],
        doingsYear: [
          { required: true, message: "活动年份不能为空", trigger: "blur" },
        ],
        doingsNo: [
          { required: true, message: "活动编号不能为空", trigger: "blur" },
        ],
        doingsName: [
          { required: true, message: "活动名称不能为空", trigger: "blur" },
        ],
        subject: [{ required: true, message: "主题不能为空", trigger: "blur" }],
        // coverRange: [
        //   { required: true, message: "涉及领域不能为空", trigger: "blur" }
        // ],
        purpoe: [
          { required: true, message: "活动目标不能为空", trigger: "blur" },
        ],
        attendGroup: [
          { required: true, message: "活动群体不能为空", trigger: "blur" },
        ],
        attendNum: [
          { required: true, message: "活动人数不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询比赛活动列表 */
    getList() {
      this.loading = true;
      listDoings(this.queryParams).then((response) => {
        this.doingsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        matchId: null,
        doingsYear: null,
        doingsNo: null,
        doingsName: null,
        subject: null,
        coverRange: null,
        purpoe: null,
        attendGroup: [],
        attendNum: [],
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
        remark: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加比赛活动";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getDoings(id).then((response) => {
        this.form = response.data;
        this.form.attendGroup = this.form.attendGroup.split(",");
        this.form.attendNum = this.form.attendNum.split(",");
        this.open = true;
        this.title = "修改比赛活动";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.attendGroup = this.form.attendGroup.join(",");
          this.form.attendNum = this.form.attendNum.join(",");
          if (this.form.id != null) {
            updateDoings(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDoings(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除比赛活动编号为"' + ids + '"的数据项？')
        .then(function () {
          return delDoings(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "business/doings/export",
        {
          ...this.queryParams,
        },
        `doings_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
