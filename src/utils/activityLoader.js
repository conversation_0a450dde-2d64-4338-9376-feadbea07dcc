import { marked } from 'marked';
import DOMPurify from 'dompurify';

const activityFiles = require.context('@/assets/activities', false, /\.md$/);

export function loadActivityContent(filename) {
  try {
    const contentModule = activityFiles(`./${filename}.md`);
    const content = typeof contentModule === 'string' ? contentModule : contentModule.default;
    const html = DOMPurify.sanitize(marked.parse(content));
    return Promise.resolve(html);
  } catch (e) {
    console.error('加载失败:', e);
    return Promise.resolve('<p>内容加载失败，请刷新重试</p>');
  }
}

