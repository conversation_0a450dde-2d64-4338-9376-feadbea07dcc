## 1.年度主题
数智北京

## 2.活动简介
鼓励参与者突破现有算法局限,开发更高效、更具创新性的AI算法，围绕智慧城市建设中的数智治理、数智乡村、公共文化服务等场景,用算法解决实际问题。

## 3.参考主题
智慧城市、医疗健康、绿色低碳、数字人文等。

## 4.作品展示说明
重点说明作品如何将AI与跨学科知识相结合。提供详实的实验数据、测试结果、对比分析等。

## 5.创作要求
1. **作品名称**: 自拟,需简洁明确反映核心内容。
2. **实践报告**: PDF格式（≤20页），需包含背景、技术路线图、完整代码、创新点及实验结果分析。
3. **演示视频**: MP4格式（≤500MB，≤10分钟），需包含自我介绍+创作思路解说+算法执行过程展示。
4. **注意事项**: 禁止上传隐私数据，需附带《创作者声明》。

## 6.评价维度
| 维度和权重       | 内容描述 |
|------------------|----------|
| **技术创新 (20%)** | 方案具有原创性或突破性。 |
| **技术优势 (20%)** | 1.精度指标(10%)<br>2.效率指标(10%)。 |
| **方案价值 (20%)** | 有明确的潜在市场需求和目标用户。 |
| **社会价值 (20%)** | 推动行业进步,普惠数字生活。 |
| **方案完整 (20%)** | 1.可视化呈现(10%)<br>2.文档完整性(10%)。 |
