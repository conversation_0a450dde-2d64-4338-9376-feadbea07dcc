## 1. 年度主题
数智教育

## 2. 活动简介
鼓励教师探索人工智能与教学深度融合的创新路径，开发具有示范价值的教学案例，提升教师运用AI技术开展教学的能力。基于人工智能技术开展跨学科主题项目，如AI+传统文化、AI+环保等，培养学生综合素养与创新能力。

## 3. 作品展示说明
- 教案内容形式丰富多样，包括教学设计、课堂实录、教学反思等。
- 基于AI技术开展教学后，学生在学业成绩、学习兴趣、创新能力等方面的变化数据，通过对比分析，体现AI教学的显著成效。
- 分享在教学过程中遇到的问题、解决方法以及对未来AI教学的展望，为其他教师提供宝贵经验。

## 4. 创作要求

### (1) 教学案例报告
- **格式要求**：DOCX文档
- **内容要求**：
  - 教师个人介绍
  - 教案（包含以下部分）：
    - **指导思想**：阐述案例所依据的教育理念、AI应用相关理论等。
    - **活动目标**：明确通过此次教学希望学生达成的知识、技能、情感态度等方面目标。
    - **活动过程**：详细描述教学环节设置、AI工具运用步骤、师生互动流程等。
    - **教学效果**：包括前后数据对比。
    - **活动反思**：分析教学过程中的优点、不足，以及后续改进方向。

### (2) 演示视频
- **格式要求**：MP4视频
- **大小限制**：不超过200MB
- **时长限制**：不超过5分钟
- **内容要求**：
  - AI工具使用效果展示
  - 课堂实施视频

### (3) 注意事项
- 作品中禁止上传含个人隐私的数据（如人脸、身份证号、手机号等），违规者取消资格。
- 每件作品需附带一份《创作者声明》。

## 5. 评价维度

| 维度和权重 | 内容描述 |
|------------|----------|
| **整体设计 (20%)** | 教学案例涉及的核心问题具有教学价值和真实性；并根据核心问题合理地设计教学项目与活动。 |
| **学习目标 (30%)** | 1. 学习目标确定的依据合理、充分，目标维度不拘泥于三维目标角度和学科核心素养形式。<br>2. 教学的重点和难点明确，在展示视频中说明了教学重点和难点的选择依据，并简要说明突破解决的方法。 |
| **教学过程 (40%)** | 1. “学生活动”和“教师组织”的设计有效为教学目标服务，时间分配合理。<br>2. 安排的实践任务与单元/整体项目设计的任务一致；实践意图与整体目标一致。<br>3. 展示视频清晰反映了教学设计和教学过程，并有呈现课堂活动实际片段，侧重展现学生学习重点、难点内容时的实际表现。<br>4. 教学效果显著，学生在知识掌握、技能提升及素养培育上均有明显进步，达成预设教学目标。 |
| **技能素养 (10%)** | 1. 恰当有效地使用多媒体课件辅助，课件制作美观大方。<br>2. 表达思路清晰，逻辑严谨；用语规范。 |