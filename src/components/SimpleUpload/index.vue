<template>
  <div class="simple-upload">
    <el-upload
      ref="upload"
      :action="uploadUrl"
      :auto-upload="false"
      :show-file-list="false"
      :before-upload="beforeUpload"
      :on-progress="handleProgress"
      :on-success="handleSuccess"
      :on-error="handleError"
      :headers="uploadHeaders"
      :disabled="disabled || isUploading"
      :multiple="false"
      :accept="accept"
    >
      <el-button
        slot="trigger"
        size="small"
        type="primary"
        :disabled="disabled || isUploading"
      >
        选取文件
      </el-button>
      <el-button
        style="margin-left: 10px;"
        size="small"
        type="success"
        @click="submitUpload"
        :disabled="!selectedFile || isUploading"
        :loading="isUploading"
      >
        {{ isUploading ? '上传中...' : '上传文件' }}
      </el-button>
      <div slot="tip" class="el-upload__tip" v-if="tip">
        {{ tip }}
      </div>
    </el-upload>

    <!-- 文件信息 -->
    <div v-if="selectedFile" class="file-info">
      <div class="file-name">
        <i class="el-icon-document"></i>
        {{ selectedFile.name }}
      </div>
      <div class="file-size">
        {{ formatSize(selectedFile.size) }}
      </div>
    </div>

    <!-- 进度条 -->
    <div v-if="isUploading || (uploadProgress > 0 && uploadProgress < 100)" class="upload-progress-container">
      <el-progress
        :percentage="uploadProgress"
        :format="progressFormat"
        :stroke-width="15"
        :status="uploadStatus"
        class="upload-progress"
      ></el-progress>
      <div class="upload-info">
        <span class="upload-speed">{{ uploadSpeed }}</span>
        <span class="upload-size">{{ uploadSizeText }}</span>
      </div>
    </div>

    <!-- 已上传文件列表 -->
    <div v-if="value && !isUploading" class="uploaded-files">
      <div class="uploaded-file">
        <el-link :href="value" :underline="false" target="_blank">
          <i class="el-icon-document"></i>
          {{ getFileName(value) }}
        </el-link>
        <el-button
          v-if="!disabled"
          type="text"
          icon="el-icon-delete"
          @click="removeFile"
          class="remove-btn"
        >
          删除
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";

export default {
  name: "SimpleUpload",
  props: {
    // 文件URL值
    value: {
      type: String,
      default: ''
    },
    // 文件大小限制(MB)
    fileSize: {
      type: Number,
      default: 500
    },
    // 文件类型限制
    fileType: {
      type: Array,
      default: () => ['mp4', 'zip']
    },
    // 接受的文件类型
    accept: {
      type: String,
      default: '.mp4,.zip'
    },
    // 提示文本
    tip: {
      type: String,
      default: '只能上传一个文件，支持mp4或zip格式，且不超过500MB'
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedFile: null,
      isUploading: false,
      uploadProgress: 0,
      uploadSpeed: '',
      uploadSizeText: '',
      uploadStatus: 'success',
      uploadStartTime: 0,
      uploadedSize: 0,
      totalSize: 0,
      uploadUrl: process.env.VUE_APP_BASE_API + '/common/upload',
      uploadHeaders: {
        Authorization: 'Bearer ' + getToken()
      }
    };
  },
  methods: {
    // 上传前验证
    beforeUpload(file) {
      console.log('选择文件:', file.name, '大小:', this.formatSize(file.size));

      // 验证文件类型
      const fileName = file.name.toLowerCase();
      const fileExtension = fileName.substring(fileName.lastIndexOf('.') + 1);

      if (this.fileType.length > 0 && !this.fileType.includes(fileExtension)) {
        this.$message.error(`只能上传${this.fileType.join('、')}格式的文件！`);
        return false;
      }

      // 验证文件大小
      const isLtSize = file.size / 1024 / 1024 < this.fileSize;
      if (!isLtSize) {
        this.$message.error(`文件大小不能超过 ${this.fileSize}MB！`);
        return false;
      }

      // 只设置选中的文件，不立即开始上传
      this.selectedFile = file;
      console.log('文件验证通过，已选择文件:', file.name);

      // 阻止自动上传，等待用户点击上传按钮
      return false;
    },

    // 提交上传
    async submitUpload() {
      if (!this.selectedFile) {
        this.$message.warning('请先选择文件');
        return;
      }

      console.log('手动提交上传');

      // 初始化上传状态
      this.isUploading = true;
      this.uploadProgress = 0;
      this.uploadStatus = 'active';
      this.uploadStartTime = Date.now();
      this.uploadedSize = 0;
      this.totalSize = this.selectedFile.size;

      try {
        // 使用axios直接上传，而不是el-upload的submit方法
        const formData = new FormData();
        formData.append('file', this.selectedFile);

        const response = await this.$axios.post('/common/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            ...this.uploadHeaders
          },
          onUploadProgress: (progressEvent) => {
            this.uploadProgress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            this.updateProgressInfo(progressEvent.loaded);
          }
        });

        // 处理上传成功
        this.handleSuccess(response.data, null, null);

      } catch (error) {
        console.error('上传失败:', error);
        this.handleError(error, null, null);
      }
    },

    // 上传进度
    handleProgress(event, file, fileList) {
      console.log('上传进度:', event.percent + '%');
      this.uploadProgress = Math.round(event.percent);
      this.updateProgressInfo(event.loaded || (event.percent / 100 * this.totalSize));
    },

    // 上传成功
    handleSuccess(response, file, fileList) {
      console.log('上传成功响应:', response);
      this.isUploading = false;
      this.uploadStatus = 'success';

      // 处理不同的响应格式
      const responseData = response || {};
      if (responseData.code === 200 || responseData.code === undefined) {
        this.uploadProgress = 100;
        const fileUrl = responseData.url || responseData.data || responseData.fileName || responseData;
        this.$emit('input', fileUrl);
        this.$message.success('上传成功');
        console.log('文件上传成功，URL:', fileUrl);

        setTimeout(() => {
          this.resetUploadState();
        }, 2000);
      } else {
        this.uploadStatus = 'exception';
        this.$message.error(responseData.msg || responseData.message || '上传失败');
        this.resetUploadState();
      }
    },

    // 上传失败
    handleError(err, file, fileList) {
      console.error('上传失败:', err);
      this.isUploading = false;
      this.uploadStatus = 'exception';
      this.$message.error('上传失败: ' + (err.message || '未知错误'));
      this.resetUploadState();
    },

    // 更新进度信息
    updateProgressInfo(uploadedBytes) {
      this.uploadedSize = uploadedBytes;

      // 计算上传速度
      const elapsed = (Date.now() - this.uploadStartTime) / 1000;
      if (elapsed > 0) {
        const speed = uploadedBytes / elapsed;
        this.uploadSpeed = this.formatSpeed(speed);
      }

      this.uploadSizeText = `${this.formatSize(uploadedBytes)} / ${this.formatSize(this.totalSize)}`;
    },

    // 格式化速度
    formatSpeed(bytesPerSecond) {
      if (bytesPerSecond < 1024) {
        return `${bytesPerSecond.toFixed(0)} B/s`;
      } else if (bytesPerSecond < 1024 * 1024) {
        return `${(bytesPerSecond / 1024).toFixed(1)} KB/s`;
      } else {
        return `${(bytesPerSecond / (1024 * 1024)).toFixed(1)} MB/s`;
      }
    },

    // 格式化文件大小
    formatSize(bytes) {
      if (bytes < 1024) {
        return `${bytes} B`;
      } else if (bytes < 1024 * 1024) {
        return `${(bytes / 1024).toFixed(1)} KB`;
      } else {
        return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
      }
    },

    // 格式化进度显示
    progressFormat(percentage) {
      if (percentage === 100) {
        return '上传完成';
      }
      return `${percentage}%`;
    },

    // 获取文件名
    getFileName(url) {
      if (url && url.lastIndexOf("/") > -1) {
        return url.slice(url.lastIndexOf("/") + 1);
      }
      return url;
    },

    // 删除文件
    removeFile() {
      this.$emit('input', '');
      this.selectedFile = null;
    },

    // 重置上传状态
    resetUploadState() {
      this.uploadProgress = 0;
      this.uploadSpeed = '';
      this.uploadSizeText = '';
      this.uploadStatus = 'success';
      this.isUploading = false;
      this.uploadStartTime = 0;
      this.uploadedSize = 0;
      this.totalSize = 0;
      this.selectedFile = null;
    }
  }
};
</script>

<style scoped>
.simple-upload {
  width: 100%;
}

.file-info {
  margin-top: 10px;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.file-name {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.file-name i {
  margin-right: 5px;
  color: #909399;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.upload-progress-container {
  margin-top: 15px;
}

.upload-progress {
  margin-bottom: 8px;
}

.upload-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
  flex-wrap: wrap;
  gap: 10px;
}

.upload-speed {
  color: #67C23A;
  font-weight: 500;
}

.upload-size {
  color: #606266;
}

.uploaded-files {
  margin-top: 10px;
}

.uploaded-file {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.uploaded-file .el-link {
  flex: 1;
  color: #606266;
}

.uploaded-file .el-link i {
  margin-right: 5px;
  color: #909399;
}

.remove-btn {
  margin-left: 10px;
  color: #f56c6c;
}

.remove-btn:hover {
  color: #f56c6c;
}
</style>
