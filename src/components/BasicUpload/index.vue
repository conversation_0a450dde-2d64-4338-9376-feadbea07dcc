<template>
  <div class="basic-upload">
    <div class="upload-section">
      <input 
        ref="fileInput"
        type="file" 
        :accept="accept"
        @change="handleFileSelect"
        style="display: none"
      />
      
      <el-button 
        type="primary" 
        size="small"
        @click="selectFile"
        :disabled="disabled || isUploading"
      >
        选取文件
      </el-button>
      
      <el-button 
        type="success" 
        size="small"
        @click="uploadFile"
        :disabled="!selectedFile || isUploading"
        :loading="isUploading"
        style="margin-left: 10px;"
      >
        {{ isUploading ? '上传中...' : '上传文件' }}
      </el-button>
      
      <div v-if="tip" class="upload-tip">{{ tip }}</div>
    </div>

    <!-- 文件信息 -->
    <div v-if="selectedFile" class="file-info">
      <div class="file-name">
        <i class="el-icon-document"></i>
        {{ selectedFile.name }}
      </div>
      <div class="file-size">
        {{ formatSize(selectedFile.size) }}
      </div>
    </div>

    <!-- 进度条 -->
    <div v-if="isUploading || uploadProgress > 0" class="progress-section">
      <el-progress
        :percentage="uploadProgress"
        :status="uploadStatus"
        :stroke-width="15"
      ></el-progress>
      <div v-if="uploadSpeed" class="progress-info">
        <span>{{ uploadSpeed }}</span>
        <span>{{ uploadSizeText }}</span>
      </div>
    </div>

    <!-- 上传结果 -->
    <div v-if="value && !isUploading" class="result-section">
      <div class="uploaded-file">
        <el-link :href="value" :underline="false" target="_blank">
          <i class="el-icon-document"></i>
          {{ getFileName(value) }}
        </el-link>
        <el-button 
          v-if="!disabled"
          type="text" 
          icon="el-icon-delete" 
          @click="removeFile"
          class="remove-btn"
        >
          删除
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";

export default {
  name: "BasicUpload",
  props: {
    value: {
      type: String,
      default: ''
    },
    fileSize: {
      type: Number,
      default: 500
    },
    fileType: {
      type: Array,
      default: () => ['mp4', 'zip']
    },
    accept: {
      type: String,
      default: '.mp4,.zip'
    },
    tip: {
      type: String,
      default: '只能上传一个文件，支持mp4或zip格式，且不超过500MB'
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedFile: null,
      isUploading: false,
      uploadProgress: 0,
      uploadStatus: 'success',
      uploadSpeed: '',
      uploadSizeText: '',
      uploadStartTime: 0,
      uploadedSize: 0
    };
  },
  methods: {
    selectFile() {
      this.$refs.fileInput.click();
    },

    handleFileSelect(event) {
      const file = event.target.files[0];
      if (!file) return;

      console.log('选择文件:', file.name, '大小:', this.formatSize(file.size));

      // 验证文件类型
      const fileName = file.name.toLowerCase();
      const fileExtension = fileName.substring(fileName.lastIndexOf('.') + 1);
      
      if (this.fileType.length > 0 && !this.fileType.includes(fileExtension)) {
        this.$message.error(`只能上传${this.fileType.join('、')}格式的文件！`);
        this.clearFileInput();
        return;
      }

      // 验证文件大小
      const isLtSize = file.size / 1024 / 1024 < this.fileSize;
      if (!isLtSize) {
        this.$message.error(`文件大小不能超过 ${this.fileSize}MB！`);
        this.clearFileInput();
        return;
      }

      this.selectedFile = file;
      console.log('文件验证通过，已选择文件:', file.name);
    },

    clearFileInput() {
      this.$refs.fileInput.value = '';
      this.selectedFile = null;
    },

    async uploadFile() {
      if (!this.selectedFile) {
        this.$message.warning('请先选择文件');
        return;
      }

      console.log('开始上传文件:', this.selectedFile.name);

      this.isUploading = true;
      this.uploadProgress = 0;
      this.uploadStatus = 'active';
      this.uploadStartTime = Date.now();
      this.uploadedSize = 0;

      try {
        const formData = new FormData();
        formData.append('file', this.selectedFile);

        const response = await this.$axios.post('/common/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            'Authorization': 'Bearer ' + getToken()
          },
          onUploadProgress: (progressEvent) => {
            this.uploadProgress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            this.updateProgressInfo(progressEvent.loaded);
            console.log('上传进度:', this.uploadProgress + '%');
          }
        });

        console.log('上传响应:', response);

        if (response.data && response.data.code === 200) {
          this.uploadProgress = 100;
          this.uploadStatus = 'success';
          const fileUrl = response.data.url || response.data.data || response.data.fileName;
          this.$emit('input', fileUrl);
          this.$message.success('上传成功');
          console.log('文件上传成功，URL:', fileUrl);
          
          setTimeout(() => {
            this.resetUploadState();
          }, 2000);
        } else {
          throw new Error(response.data?.msg || '上传失败');
        }

      } catch (error) {
        console.error('上传失败:', error);
        this.uploadStatus = 'exception';
        this.$message.error('上传失败: ' + (error.message || '未知错误'));
        this.resetUploadState();
      }
    },

    updateProgressInfo(uploadedBytes) {
      this.uploadedSize = uploadedBytes;
      
      const elapsed = (Date.now() - this.uploadStartTime) / 1000;
      if (elapsed > 0) {
        const speed = uploadedBytes / elapsed;
        this.uploadSpeed = this.formatSpeed(speed);
      }

      this.uploadSizeText = `${this.formatSize(uploadedBytes)} / ${this.formatSize(this.selectedFile.size)}`;
    },

    formatSpeed(bytesPerSecond) {
      if (bytesPerSecond < 1024) {
        return `${bytesPerSecond.toFixed(0)} B/s`;
      } else if (bytesPerSecond < 1024 * 1024) {
        return `${(bytesPerSecond / 1024).toFixed(1)} KB/s`;
      } else {
        return `${(bytesPerSecond / (1024 * 1024)).toFixed(1)} MB/s`;
      }
    },

    formatSize(bytes) {
      if (bytes < 1024) {
        return `${bytes} B`;
      } else if (bytes < 1024 * 1024) {
        return `${(bytes / 1024).toFixed(1)} KB`;
      } else {
        return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
      }
    },

    getFileName(url) {
      if (url && url.lastIndexOf("/") > -1) {
        return url.slice(url.lastIndexOf("/") + 1);
      }
      return url;
    },

    removeFile() {
      this.$emit('input', '');
      this.selectedFile = null;
      this.clearFileInput();
    },

    resetUploadState() {
      this.uploadProgress = 0;
      this.uploadSpeed = '';
      this.uploadSizeText = '';
      this.uploadStatus = 'success';
      this.isUploading = false;
      this.uploadStartTime = 0;
      this.uploadedSize = 0;
      this.selectedFile = null;
      this.clearFileInput();
    }
  }
};
</script>

<style scoped>
.basic-upload {
  width: 100%;
}

.upload-section {
  margin-bottom: 15px;
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.file-info {
  margin: 15px 0;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.file-name {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
}

.file-name i {
  margin-right: 5px;
  color: #909399;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.progress-section {
  margin: 15px 0;
}

.progress-info {
  margin-top: 8px;
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
}

.result-section {
  margin-top: 15px;
}

.uploaded-file {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.uploaded-file .el-link {
  flex: 1;
  color: #606266;
}

.uploaded-file .el-link i {
  margin-right: 5px;
  color: #909399;
}

.remove-btn {
  margin-left: 10px;
  color: #f56c6c;
}

.remove-btn:hover {
  color: #f56c6c;
}
</style>
