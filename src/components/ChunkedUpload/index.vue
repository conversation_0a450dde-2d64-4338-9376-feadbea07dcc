<template>
  <div class="chunked-upload">
    <!-- 上传文件按钮 -->
    <el-button
      type="success"
      size="small"
      @click="openUploadDialog"
      :disabled="disabled"
    >
      上传文件
    </el-button>

    <!-- 文件URL显示 -->
    <div v-if="fileUrl" class="file-url-display">
      <el-link :href="fileUrl" target="_blank" :underline="false">
        <i class="el-icon-document"></i>
        {{ fileName || '查看文件' }}
      </el-link>
      <el-button
        v-if="!disabled"
        type="text"
        size="mini"
        @click="removeFile"
        style="margin-left: 10px; color: #f56c6c;"
      >
        删除
      </el-button>
    </div>

    <!-- 分片上传对话框 -->
    <el-dialog
      title="文件上传"
      :visible.sync="uploadDialogVisible"
      width="550px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
      class="chunked-upload-dialog"
    >
      <div class="upload-container">
        <!-- 文件选择 -->
        <div class="file-select-area">
          <input
            type="file"
            ref="fileInput"
            @change="handleFileSelect"
            :accept="acceptTypes"
            style="display: none;"
          >
          <el-button
            type="primary"
            icon="el-icon-upload2"
            @click="$refs.fileInput.click()"
            :disabled="isUploading"
            size="small"
          >
            选择文件
          </el-button>
          <span class="file-tip">
            支持{{ fileTypeText }}格式，大小不超过{{ fileSize }}MB
          </span>
        </div>

        <!-- 文件信息 -->
        <div v-if="selectedFile" class="file-info">
          <div class="file-info-row">
            <span class="file-name">
              <i class="el-icon-document"></i>
              {{ selectedFile.name }}
            </span>
            <span class="file-size">{{ formatFileSize(selectedFile.size) }}</span>
          </div>
          <div class="file-meta">
            分片数量：{{ totalChunks }}
          </div>
        </div>

        <!-- 上传控制按钮 -->
        <div v-if="selectedFile" class="upload-controls">
          <el-button
            type="success"
            size="small"
            @click="startUpload"
            :disabled="isUploading || isPaused"
            :loading="isUploading && !isPaused"
          >
            {{ isUploading ? '上传中...' : '开始上传' }}
          </el-button>
          <el-button
            v-if="isUploading"
            size="small"
            @click="pauseUpload"
            :disabled="isPaused"
          >
            暂停
          </el-button>
          <el-button
            v-if="isPaused"
            size="small"
            @click="resumeUpload"
          >
            继续
          </el-button>
          <el-button
            v-if="isUploading || isPaused"
            type="danger"
            size="small"
            @click="cancelUpload"
          >
            取消
          </el-button>
        </div>

        <!-- 上传进度 -->
        <div v-if="selectedFile && (isUploading || isPaused || uploadCompleted)" class="progress-section">
          <div class="progress-info">
            <span>{{ Math.round((uploadedChunks / totalChunks) * 100) }}%</span>
            <span v-if="uploadSpeed" class="upload-speed">{{ uploadSpeed }}</span>
          </div>
          <el-progress
            :percentage="Math.round((uploadedChunks / totalChunks) * 100)"
            :status="uploadStatus || undefined"
            :stroke-width="6"
            :show-text="false"
          ></el-progress>
        </div>

        <!-- 上传日志 -->
        <div v-if="uploadLogs.length > 0" class="log-section">
          <div class="log-header">
            <span>上传日志</span>
            <el-button
              type="text"
              size="mini"
              @click="uploadLogs = []"
              style="padding: 0; margin-left: 10px;"
            >
              清空
            </el-button>
          </div>
          <div class="log-content" ref="logContainer">
            <div
              v-for="(log, index) in uploadLogs"
              :key="index"
              class="log-item"
              :class="log.type"
            >
              [{{ log.time }}] {{ log.message }}
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog" :disabled="isUploading">取消</el-button>
        <el-button
          type="primary"
          @click="confirmUpload"
          :disabled="!uploadCompleted"
        >
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import request from "@/utils/request";

export default {
  name: "ChunkedUpload",
  props: {
    value: {
      type: String,
      default: ''
    },
    fileSize: {
      type: Number,
      default: 500
    },
    fileType: {
      type: Array,
      default: () => ['mp4', 'zip']
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      uploadDialogVisible: false,
      selectedFile: null,
      fileUrl: '',
      fileName: '',

      // 上传状态
      isUploading: false,
      isPaused: false,
      uploadCompleted: false,
      uploadStatus: null,

      // 分片相关
      chunkSize: 5 * 1024 * 1024, // 5MB
      totalChunks: 0,
      uploadedChunks: 0,
      uploadedParts: new Set(),

      // 上传信息
      uploadId: null,
      fileId: null,
      platform: null,
      filePath: null,
      filename: null,
      basePath: null,
      originalFilename: null,
      fileSizeValue: null,
      ext: null,
      url: null,

      // 进度和速度
      uploadSpeed: '',
      startTime: null,

      // 日志
      uploadLogs: []
    }
  },
  computed: {
    acceptTypes() {
      return this.fileType.map(type => `.${type}`).join(',')
    },
    fileTypeText() {
      return this.fileType.join('、')
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(val) {
        if (val) {
          this.fileUrl = val
          // 从URL中提取文件名
          if (val.includes('/')) {
            this.fileName = val.substring(val.lastIndexOf('/') + 1)
          }
        } else {
          this.fileUrl = ''
          this.fileName = ''
        }
      }
    }
  },
  methods: {
    // 打开上传对话框
    openUploadDialog() {
      this.uploadDialogVisible = true
      this.fullReset()
    },

    // 关闭对话框
    closeDialog() {
      if (this.isUploading) {
        this.$confirm('上传正在进行中，确定要关闭吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.cancelUpload()
          this.uploadDialogVisible = false
        })
      } else {
        this.uploadDialogVisible = false
      }
    },

    // 确认上传
    confirmUpload() {
      if (this.uploadCompleted && this.url) {
        this.fileUrl = this.url
        this.fileName = this.originalFilename
        this.$emit('input', this.url)
        this.uploadDialogVisible = false
        this.$message.success('文件上传成功')
      }
    },

    // 移除文件
    removeFile() {
      this.fileUrl = ''
      this.fileName = ''
      this.$emit('input', '')
    },

    // 文件选择处理
    handleFileSelect(event) {
      const file = event.target.files[0]
      if (!file) return

      // 验证文件类型
      const fileExt = file.name.split('.').pop().toLowerCase()
      if (!this.fileType.includes(fileExt)) {
        this.$message.error(`文件格式不正确，请上传${this.fileTypeText}格式文件`)
        return
      }

      // 验证文件大小
      if (file.size > this.fileSize * 1024 * 1024) {
        this.$message.error(`文件大小不能超过${this.fileSize}MB`)
        return
      }

      this.selectedFile = file
      this.totalChunks = Math.ceil(file.size / this.chunkSize)
      this.addLog('info', `文件已选择: ${file.name} (${this.formatFileSize(file.size)})`)
    },

    // 开始上传
    async startUpload() {
      if (!this.selectedFile) {
        this.$message.error('请先选择文件')
        return
      }

      this.addLog('info', '开始上传...')
      this.isUploading = true
      this.isPaused = false
      this.uploadedChunks = 0
      this.uploadedParts.clear()
      this.startTime = Date.now()
      this.uploadStatus = null

      // 初始化分片上传
      const initSuccess = await this.initMultipartUpload()
      if (!initSuccess) {
        this.isUploading = false
        this.uploadStatus = 'exception'
        return
      }

      // 开始上传分片
      await this.uploadChunks()
    },

    // 暂停上传
    pauseUpload() {
      this.isPaused = true
      this.isUploading = false
      this.addLog('warning', '上传已暂停')
    },

    // 继续上传
    async resumeUpload() {
      this.isPaused = false
      this.isUploading = true
      this.addLog('info', '继续上传...')

      // 从未上传的分片开始继续上传
      await this.uploadChunks()
    },

    // 取消上传
    async cancelUpload() {
      this.isUploading = false
      this.isPaused = false

      if (this.uploadId) {
        await this.abortMultipartUpload()
      }

      this.resetUploadState()
      this.addLog('error', '上传已取消')
    },

    // 初始化分片上传
    async initMultipartUpload() {
      try {
        const response = await request({
          url: '/common/multipart/init',
          method: 'post',
          data: {
            originalFilename: this.selectedFile.name,
            fileSize: this.selectedFile.size
          },
          headers: {
            repeatSubmit: false
          }
        })

        const result = response

        if (result.code === 200) {
          this.uploadId = result.data.uploadId
          this.fileId = result.data.fileId
          this.platform = result.data.platform
          this.filePath = result.data.path
          this.filename = result.data.filename
          this.basePath = result.data.basePath
          this.originalFilename = result.data.originalFilename
          this.fileSizeValue = result.data.size
          this.ext = result.data.ext
          this.url = result.data.url
          this.addLog('success', `分片上传初始化成功: uploadId=${this.uploadId}`)
          return true
        } else {
          this.addLog('error', `初始化失败: ${result.msg}`)
          return false
        }
      } catch (error) {
        console.error('初始化分片上传错误:', error)
        const errorMsg = error.response?.data?.msg || error.message || '初始化失败'
        this.addLog('error', `初始化分片上传时发生错误: ${errorMsg}`)
        this.$message.error(`初始化失败: ${errorMsg}`)
        return false
      }
    },

    // 上传分片
    async uploadChunks() {
      for (let i = this.uploadedChunks; i < this.totalChunks && this.isUploading && !this.isPaused; i++) {
        const start = i * this.chunkSize
        const end = Math.min(start + this.chunkSize, this.selectedFile.size)
        const chunk = this.selectedFile.slice(start, end)

        const success = await this.uploadChunk(i, chunk)
        if (!success) {
          this.addLog('error', '上传失败，停止上传')
          this.uploadStatus = 'exception'
          this.isUploading = false
          return
        }
      }

      if (this.uploadedChunks === this.totalChunks && this.isUploading) {
        // 完成上传
        const completeSuccess = await this.completeMultipartUpload()
        if (completeSuccess) {
          this.addLog('success', '所有分片上传完成！')
          this.uploadCompleted = true
          this.uploadStatus = 'success'
        }
        this.isUploading = false
      }
    },

    // 上传单个分片
    async uploadChunk(chunkIndex, chunk) {
      const formData = new FormData()
      formData.append('file', chunk)
      formData.append('uploadId', this.uploadId)
      formData.append('partNumber', chunkIndex + 1)
      formData.append('fileId', this.fileId)
      formData.append('platform', this.platform)
      formData.append('path', this.filePath)
      formData.append('filename', this.filename)

      try {
        const response = await request({
          url: '/common/multipart/upload',
          method: 'post',
          data: formData,
          headers: {
            'Content-Type': 'multipart/form-data',
            repeatSubmit: false
          }
        })

        const result = response

        if (result.code === 200) {
          this.uploadedParts.add(chunkIndex + 1)
          this.uploadedChunks++
          this.updateProgress()
          this.addLog('success', `分片 ${chunkIndex + 1} 上传成功 (${this.formatFileSize(chunk.size)})`)
          return true
        } else {
          this.addLog('error', `分片 ${chunkIndex + 1} 上传失败: ${result.msg}`)
          return false
        }
      } catch (error) {
        console.error(`分片 ${chunkIndex + 1} 上传错误:`, error)
        const errorMsg = error.response?.data?.msg || error.message || '上传失败'
        this.addLog('error', `分片 ${chunkIndex + 1} 上传时发生错误: ${errorMsg}`)
        return false
      }
    },

    // 完成分片上传
    async completeMultipartUpload() {
      try {
        const response = await request({
          url: '/common/multipart/complete',
          method: 'post',
          data: {
            uploadId: this.uploadId,
            fileId: this.fileId,
            platform: this.platform,
            path: this.filePath,
            filename: this.filename,
            basePath: this.basePath,
            originalFilename: this.originalFilename,
            size: this.fileSizeValue,
            ext: this.ext,
            url: this.url
          },
          headers: {
            repeatSubmit: false
          }
        })

        const result = response

        if (result.code === 200) {
          this.url = result.data.url
          this.addLog('success', `文件上传完成! URL: ${result.data.url}`)
          return true
        } else {
          this.addLog('error', `完成上传失败: ${result.msg}`)
          return false
        }
      } catch (error) {
        this.addLog('error', `完成上传时发生错误: ${error.message}`)
        return false
      }
    },

    // 取消分片上传
    async abortMultipartUpload() {
      try {
        const response = await request({
          url: '/common/multipart/abort',
          method: 'post',
          data: {
            uploadId: this.uploadId,
            fileId: this.fileId,
            platform: this.platform,
            path: this.filePath,
            filename: this.filename,
            basePath: this.basePath,
            originalFilename: this.originalFilename,
            size: this.fileSizeValue,
            ext: this.ext,
            url: this.url
          },
          headers: {
            repeatSubmit: false
          }
        })

        const result = response

        if (result.code === 200) {
          this.addLog('warning', '上传已取消')
          return true
        } else {
          this.addLog('error', `取消上传失败: ${result.msg}`)
          return false
        }
      } catch (error) {
        this.addLog('error', `取消上传时发生错误: ${error.message}`)
        return false
      }
    },

    // 更新进度
    updateProgress() {
      const elapsed = (Date.now() - this.startTime) / 1000
      if (elapsed > 0) {
        const uploadedBytes = this.uploadedChunks * this.chunkSize
        const speed = uploadedBytes / elapsed
        this.uploadSpeed = this.formatSpeed(speed)
      }
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    // 格式化速度
    formatSpeed(bytesPerSecond) {
      if (bytesPerSecond < 1024) {
        return `${bytesPerSecond.toFixed(0)} B/s`
      } else if (bytesPerSecond < 1024 * 1024) {
        return `${(bytesPerSecond / 1024).toFixed(1)} KB/s`
      } else {
        return `${(bytesPerSecond / (1024 * 1024)).toFixed(1)} MB/s`
      }
    },

    // 添加日志
    addLog(type, message) {
      const now = new Date().toLocaleTimeString()
      this.uploadLogs.push({
        type: type,
        time: now,
        message: message
      })

      // 自动滚动到底部
      this.$nextTick(() => {
        if (this.$refs.logContainer) {
          this.$refs.logContainer.scrollTop = this.$refs.logContainer.scrollHeight
        }
      })
    },

    // 重置上传状态（保留已选择的文件）
    resetUploadState() {
      this.isUploading = false
      this.isPaused = false
      this.uploadCompleted = false
      this.uploadStatus = null
      this.uploadId = null
      this.fileId = null
      this.platform = null
      this.filePath = null
      this.filename = null
      this.basePath = null
      this.originalFilename = null
      this.fileSizeValue = null
      this.ext = null
      this.url = null
      this.uploadedChunks = 0
      this.uploadedParts.clear()
      this.uploadSpeed = ''
      this.startTime = null
      this.uploadLogs = []
      // 注意：不清除 selectedFile，让用户可以重新尝试上传
    },

    // 完全重置（包括清除文件）
    fullReset() {
      this.resetUploadState()
      this.selectedFile = null

      // 重置文件输入
      if (this.$refs.fileInput) {
        this.$refs.fileInput.value = ''
      }
    }
  }
}
</script>

<style scoped>
.chunked-upload {
  width: 100%;
}

.file-url-display {
  margin-top: 8px;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.upload-container {
  padding: 5px 0;
}

.file-select-area {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 15px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  margin-bottom: 15px;
  background-color: #fafbfc;
}

.file-tip {
  color: #909399;
  font-size: 12px;
  margin: 0;
}

.file-info {
  background-color: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 15px;
  border: 1px solid #e4e7ed;
}

.file-info-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
}

.file-name {
  color: #303133;
  font-size: 14px;
  font-weight: 500;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 10px;
}

.file-name i {
  color: #409eff;
  margin-right: 5px;
}

.file-size {
  color: #67c23a;
  font-size: 12px;
  font-weight: 500;
}

.file-meta {
  color: #909399;
  font-size: 12px;
}

.upload-controls {
  text-align: center;
  margin-bottom: 15px;
}

.upload-controls .el-button {
  margin: 0 4px;
}

.progress-section {
  margin-bottom: 15px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.upload-speed {
  color: #67c23a;
  font-size: 12px;
}

.log-section {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.log-header {
  background-color: #f5f7fa;
  padding: 6px 12px;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 500;
  font-size: 13px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.log-content {
  height: 120px;
  overflow-y: auto;
  padding: 6px 12px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 11px;
  background-color: #fafafa;
  line-height: 1.4;
}

.log-item {
  margin-bottom: 2px;
  line-height: 1.3;
}

.log-item.info {
  color: #606266;
}

.log-item.success {
  color: #67c23a;
}

.log-item.error {
  color: #f56c6c;
}

.log-item.warning {
  color: #e6a23c;
}

/* 对话框样式优化 */
.chunked-upload-dialog .el-dialog__body {
  padding: 15px 20px;
}

.chunked-upload-dialog .el-dialog__header {
  padding: 15px 20px 10px;
}

.chunked-upload-dialog .el-dialog__footer {
  padding: 10px 20px 15px;
}
</style>
