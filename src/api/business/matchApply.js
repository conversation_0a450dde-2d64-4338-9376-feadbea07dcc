import request from '@/utils/request'

// 查询比赛申报列表
export function listMatchApply(query) {
  return request({
    url: '/business/matchApply/list',
    method: 'get',
    params: query
  })
}

// 查询比赛申报详细
export function getMatchApply(id) {
  return request({
    url: '/business/matchApply/' + id,
    method: 'get'
  })
}

// 新增比赛申报
export function addMatchApply(data) {
  return request({
    url: '/business/matchApply',
    method: 'post',
    data: data
  })
}

// 修改比赛申报
export function updateMatchApply(data) {
  return request({
    url: '/business/matchApply',
    method: 'put',
    data: data
  })
}

// 删除比赛申报
export function delMatchApply(id) {
  return request({
    url: '/business/matchApply/' + id,
    method: 'delete'
  })
}

// 查询申报审核详情
export function getAuditNodeData(applyNo) {
  return request({
    url: '/business/matchApply/getAuditNodeData/' + applyNo,
    method: 'get'
  })
}

// 区审核
export function areaAudit(data) {
  return request({
    url: '/business/matchApply/areaAudit',
    method: 'post',
    data: data
  })
}

// 市审核
export function cityAudit(data) {
  return request({
    url: '/business/matchApply/cityAudit',
    method: 'post',
    data: data
  })
}
