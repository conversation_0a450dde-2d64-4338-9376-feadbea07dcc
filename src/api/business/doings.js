import request from '@/utils/request'

// 查询比赛活动列表
export function listDoings(query) {
  return request({
    url: '/business/doings/list',
    method: 'get',
    params: query
  })
}

// 查询比赛活动详细
export function getDoings(id) {
  return request({
    url: '/business/doings/' + id,
    method: 'get'
  })
}

// 新增比赛活动
export function addDoings(data) {
  return request({
    url: '/business/doings',
    method: 'post',
    data: data
  })
}

// 修改比赛活动
export function updateDoings(data) {
  return request({
    url: '/business/doings',
    method: 'put',
    data: data
  })
}

// 删除比赛活动
export function delDoings(id) {
  return request({
    url: '/business/doings/' + id,
    method: 'delete'
  })
}
