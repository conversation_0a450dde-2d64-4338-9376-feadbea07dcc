package com.ruoyi.project.system.task;

import com.ruoyi.project.system.service.IExportTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 导出任务清理定时任务
 * 
 * <AUTHOR>
 */
@Component
public class ExportTaskCleanupTask {
    
    private static final Logger log = LoggerFactory.getLogger(ExportTaskCleanupTask.class);
    
    @Autowired
    private IExportTaskService exportTaskService;
    
    /**
     * 清理过期的导出任务和文件
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanExpiredTasks() {
        log.info("开始清理过期的导出任务和文件");
        try {
            exportTaskService.cleanExpiredTasks();
            log.info("清理过期的导出任务和文件完成");
        } catch (Exception e) {
            log.error("清理过期的导出任务和文件失败", e);
        }
    }
}
